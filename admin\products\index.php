<?php
/**
 * Admin Products Management - إدارة المنتجات
 */

require_once '../../config/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/image_helper.php';
require_once '../../classes/Product.php';
require_once '../../classes/Category.php';

// Require admin privileges
require_admin();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize classes
$product_class = new Product($db);
$category_class = new Category($db);

// Get pagination parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : null;

// Handle product actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['delete_product'])) {
        $product_id = (int)$_POST['product_id'];
        if ($product_class->deleteProduct($product_id)) {
            $success_message = "تم حذف المنتج بنجاح!";
        } else {
            $error_message = "حدث خطأ أثناء حذف المنتج!";
        }
    }
}

// Get products
$products = $product_class->getProducts($page, PRODUCTS_PER_PAGE, $category_filter, $search);
$total_products = $product_class->getTotalCount($category_filter, $search);
$total_pages = ceil($total_products / PRODUCTS_PER_PAGE);

// Get categories for filter
$categories = $category_class->getAllCategories();

$page_title = "إدارة المنتجات - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Admin RTL CSS -->
    <link href="../../assets/css/admin-rtl.css" rel="stylesheet">
    
    <style>
        .admin-header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .product-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
        }
        
        .product-image {
            height: 150px;
            object-fit: cover;
            border-radius: 10px;
        }
        
        .product-status {
            position: absolute;
            top: 10px;
            left: 10px;
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .btn-action {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            margin: 0 0.125rem;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="../" class="btn btn-outline-secondary me-3">
                        <i class="fas fa-arrow-right ms-1"></i>
                        العودة
                    </a>
                    <h4 class="mb-0">إدارة المنتجات</h4>
                </div>
                
                <div class="d-flex align-items-center">
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus ms-2"></i>
                        إضافة منتج جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Messages -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle ms-2"></i>
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle ms-2"></i>
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Filters -->
        <div class="filter-section">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="البحث في المنتجات...">
                </div>
                
                <div class="col-md-3">
                    <label for="category" class="form-label">التصنيف</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">جميع التصنيفات</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>" 
                                    <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                <?php echo $category['name']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search ms-2"></i>
                            بحث
                        </button>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times ms-2"></i>
                            مسح
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Products Grid -->
        <?php if (!empty($products)): ?>
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5>المنتجات (<?php echo $total_products; ?>)</h5>
                <div class="btn-group" role="group">
                    <input type="radio" class="btn-check" name="view-mode" id="grid-view" checked>
                    <label class="btn btn-outline-secondary" for="grid-view">
                        <i class="fas fa-th"></i>
                    </label>
                    
                    <input type="radio" class="btn-check" name="view-mode" id="list-view">
                    <label class="btn btn-outline-secondary" for="list-view">
                        <i class="fas fa-list"></i>
                    </label>
                </div>
            </div>

            <!-- Grid View -->
            <div id="gridView" class="row">
                <?php foreach ($products as $product): ?>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card product-card h-100">
                            <div class="position-relative">
                                <?php echo generate_product_image_html(
                                    $product['primary_image'],
                                    $product['name'],
                                    'card-img-top product-image'
                                ); ?>
                                
                                <span class="product-status <?php echo $product['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo $product['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </div>
                            
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title"><?php echo $product['name']; ?></h6>
                                <p class="card-text text-muted small flex-grow-1">
                                    <?php echo substr($product['short_description'], 0, 80); ?>...
                                </p>
                                
                                <div class="mb-2">
                                    <small class="text-muted">التصنيف: <?php echo $product['category_name']; ?></small>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="h6 text-primary mb-0">
                                        <?php echo format_price($product['sale_price'] ?: $product['price']); ?>
                                    </span>
                                    <small class="text-muted">المخزون: <?php echo $product['stock_quantity']; ?></small>
                                </div>
                                
                                <div class="d-flex gap-1 mt-auto">
                                    <a href="../../product.php?id=<?php echo $product['id']; ?>"
                                       class="btn btn-outline-info btn-action" title="عرض" target="_blank">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="edit.php?id=<?php echo $product['id']; ?>"
                                       class="btn btn-outline-primary btn-action" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-danger btn-action" 
                                            onclick="deleteProduct(<?php echo $product['id']; ?>, '<?php echo addslashes($product['name']); ?>')" 
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- List View (Hidden by default) -->
            <div id="listView" class="d-none">
                <div class="card">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الصورة</th>
                                    <th>اسم المنتج</th>
                                    <th>التصنيف</th>
                                    <th>السعر</th>
                                    <th>المخزون</th>
                                    <th>الحالة</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($products as $product): ?>
                                    <tr>
                                        <td>
                                            <?php echo generate_product_image_html(
                                                $product['primary_image'],
                                                $product['name'],
                                                'rounded',
                                                'width: 50px; height: 50px; object-fit: cover;'
                                            ); ?>
                                        </td>
                                        <td>
                                            <strong><?php echo $product['name']; ?></strong>
                                            <?php if ($product['sku']): ?>
                                                <br><small class="text-muted">SKU: <?php echo $product['sku']; ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $product['category_name']; ?></td>
                                        <td>
                                            <?php if ($product['sale_price']): ?>
                                                <span class="text-danger"><?php echo format_price($product['sale_price']); ?></span>
                                                <br><small class="text-muted text-decoration-line-through">
                                                    <?php echo format_price($product['price']); ?>
                                                </small>
                                            <?php else: ?>
                                                <?php echo format_price($product['price']); ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $product['stock_quantity'] > 0 ? 'success' : 'danger'; ?>">
                                                <?php echo $product['stock_quantity']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $product['is_active'] ? 'success' : 'secondary'; ?>">
                                                <?php echo $product['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <a href="view.php?id=<?php echo $product['id']; ?>" 
                                                   class="btn btn-outline-info btn-sm" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit.php?id=<?php echo $product['id']; ?>" 
                                                   class="btn btn-outline-primary btn-sm" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                                        onclick="deleteProduct(<?php echo $product['id']; ?>, '<?php echo addslashes($product['name']); ?>')" 
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="d-flex justify-content-center mt-4">
                    <?php 
                    $base_url = 'index.php?';
                    if ($search) $base_url .= 'search=' . urlencode($search) . '&';
                    if ($category_filter) $base_url .= 'category=' . $category_filter . '&';
                    echo generate_pagination($page, $total_pages, rtrim($base_url, '&'));
                    ?>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-box fa-4x text-muted mb-4"></i>
                <h4>لا توجد منتجات</h4>
                <p class="text-muted mb-4">
                    <?php if ($search || $category_filter): ?>
                        لم يتم العثور على منتجات تطابق معايير البحث
                    <?php else: ?>
                        لم يتم إضافة أي منتجات بعد
                    <?php endif; ?>
                </p>
                <a href="add.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus ms-2"></i>
                    إضافة منتج جديد
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف المنتج "<span id="productName"></span>"؟</p>
                    <p class="text-danger small">هذا الإجراء لا يمكن التراجع عنه.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" class="d-inline">
                        <input type="hidden" name="product_id" id="deleteProductId">
                        <button type="submit" name="delete_product" class="btn btn-danger">
                            <i class="fas fa-trash ms-2"></i>
                            حذف
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // View mode toggle
        document.getElementById('grid-view').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('gridView').classList.remove('d-none');
                document.getElementById('listView').classList.add('d-none');
            }
        });
        
        document.getElementById('list-view').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('gridView').classList.add('d-none');
                document.getElementById('listView').classList.remove('d-none');
            }
        });
        
        // Delete product function
        function deleteProduct(productId, productName) {
            document.getElementById('productName').textContent = productName;
            document.getElementById('deleteProductId').value = productId;
            
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
    </script>
</body>
</html>
