<?php
/**
 * Test Cart API - اختبار واجهة السلة
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'classes/Product.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();
$product_class = new Product($db);

echo "<h1>اختبار واجهة السلة</h1>";

// Test 1: Check products
echo "<h2>1. فحص المنتجات</h2>";
$products = $product_class->getProducts(1, 5);
echo "<table border='1'>";
echo "<tr><th>ID</th><th>الاسم</th><th>نشط</th><th>الكمية</th><th>تفاصيل</th></tr>";
foreach ($products as $product) {
    echo "<tr>";
    echo "<td>" . $product['id'] . "</td>";
    echo "<td>" . $product['name'] . "</td>";
    echo "<td>" . (isset($product['is_active']) ? ($product['is_active'] ? 'نعم' : 'لا') : 'غير محدد') . "</td>";
    echo "<td>" . $product['stock_quantity'] . "</td>";
    echo "<td>";
    // Get full product details
    $full_product = $product_class->getProductById($product['id']);
    echo "is_active في التفاصيل: " . (isset($full_product['is_active']) ? ($full_product['is_active'] ? 'نعم' : 'لا') : 'غير محدد');
    echo "</td>";
    echo "</tr>";
}
echo "</table>";

// Test 2: Test add to cart function
echo "<h2>2. اختبار إضافة للسلة</h2>";
if (!empty($products)) {
    $test_product = $products[0];
    echo "اختبار المنتج: " . $test_product['name'] . " (ID: " . $test_product['id'] . ")<br>";
    
    // Simulate adding to cart
    if (add_to_cart($test_product['id'], null, 1)) {
        echo "✅ تم إضافة المنتج للسلة بنجاح<br>";
        echo "عدد عناصر السلة: " . get_cart_count() . "<br>";
    } else {
        echo "❌ فشل في إضافة المنتج للسلة<br>";
    }
}

// Test 3: Check session
echo "<h2>3. فحص الجلسة</h2>";
echo "معرف الجلسة: " . session_id() . "<br>";
echo "محتوى السلة: ";
var_dump($_SESSION['cart'] ?? 'فارغة');

// Test 4: Manual API test
echo "<h2>4. اختبار API يدوي</h2>";
echo '<form method="POST" action="api/cart.php">';
echo '<input type="hidden" name="action" value="add">';
echo '<input type="hidden" name="product_id" value="' . ($products[0]['id'] ?? 1) . '">';
echo '<input type="hidden" name="quantity" value="1">';
echo '<button type="submit">اختبار إضافة للسلة</button>';
echo '</form>';

echo "<br><a href='index.php'>العودة للصفحة الرئيسية</a>";
?>
