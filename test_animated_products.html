<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المنتجات المتحركة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .demo-section {
            padding: 3rem 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .feature-demo {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            margin: 1rem 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .demo-card {
            width: 250px;
            margin: 1rem;
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="demo-section">
        <div class="container">
            <div class="text-center">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="fas fa-magic ms-2"></i>
                    المنتجات المتحركة الجديدة
                </h1>
                <p class="lead">اختبر جميع التأثيرات والتحسينات الجديدة</p>
            </div>
        </div>
    </section>
    
    <!-- Features Demo -->
    <section class="py-5">
        <div class="container">
            <!-- Animation Features -->
            <div class="feature-demo">
                <h3 class="text-primary mb-4">
                    <i class="fas fa-play-circle ms-2"></i>
                    التأثيرات المتحركة الجديدة
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="text-success">✅ تأثيرات البطاقات:</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success ms-2"></i> تحريك عند التمرير (Scroll Animation)</li>
                            <li><i class="fas fa-check text-success ms-2"></i> تأثيرات Hover متقدمة</li>
                            <li><i class="fas fa-check text-success ms-2"></i> تكبير الصور عند التمرير</li>
                            <li><i class="fas fa-check text-success ms-2"></i> انتقالات سلسة</li>
                            <li><i class="fas fa-check text-success ms-2"></i> تأثيرات الظلال المتحركة</li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="text-info">✨ ميزات تفاعلية:</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-heart text-danger ms-2"></i> زر المفضلة مع تأثير القلب</li>
                            <li><i class="fas fa-eye text-primary ms-2"></i> عرض سريع (Quick View)</li>
                            <li><i class="fas fa-shopping-cart text-success ms-2"></i> إضافة للسلة مع تأثيرات</li>
                            <li><i class="fas fa-tag text-warning ms-2"></i> شارات الخصم المتحركة</li>
                            <li><i class="fas fa-bell text-info ms-2"></i> إشعارات Toast محسنة</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Live Demo -->
            <div class="feature-demo">
                <h3 class="text-primary mb-4">
                    <i class="fas fa-desktop ms-2"></i>
                    عرض مباشر للتأثيرات
                </h3>
                
                <div class="text-center">
                    <!-- Demo Product Card -->
                    <div class="demo-card">
                        <div class="card product-card h-100 animate-fade-in">
                            <div class="product-image-container">
                                <img src="assets/images/placeholder.svg" 
                                     class="card-img-top product-image" 
                                     alt="منتج تجريبي">
                                
                                <!-- Sale Badge -->
                                <div class="sale-badge">
                                    خصم 25%
                                </div>
                                
                                <!-- Wishlist Button -->
                                <button class="wishlist-btn" onclick="toggleWishlistDemo(this)">
                                    <i class="far fa-heart"></i>
                                </button>
                                
                                <!-- Quick View Overlay -->
                                <div class="product-image-overlay">
                                    <button class="quick-view-btn" onclick="quickViewDemo()">
                                        <i class="fas fa-eye ms-2"></i>
                                        عرض سريع
                                    </button>
                                </div>
                            </div>
                            
                            <div class="card-body product-card-body">
                                <h6 class="card-title product-title">منتج تجريبي</h6>
                                <p class="card-text text-muted small">وصف قصير للمنتج التجريبي...</p>
                                
                                <div class="rating mb-2">
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="fas fa-star text-warning"></i>
                                    <i class="far fa-star text-muted"></i>
                                    <small class="text-muted ms-2">(4.0)</small>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="price product-price">
                                        <span class="text-danger fw-bold">150 ر.س</span>
                                        <small class="text-muted text-decoration-line-through ms-2">200 ر.س</small>
                                    </div>
                                    <div class="stock-status">
                                        <small class="text-success">
                                            <i class="fas fa-check-circle ms-1"></i>
                                            متوفر
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn add-to-cart-btn w-100" onclick="addToCartDemo(this)">
                                        <i class="fas fa-shopping-cart ms-2"></i>
                                        أضف للسلة
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye ms-2"></i>
                                        عرض التفاصيل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h5 class="text-center mb-3">جرب التأثيرات:</h5>
                    <div class="d-flex justify-content-center gap-3 flex-wrap">
                        <button class="btn btn-primary" onclick="animateCard()">
                            <i class="fas fa-play ms-2"></i>
                            تحريك البطاقة
                        </button>
                        <button class="btn btn-success" onclick="simulateAddToCart()">
                            <i class="fas fa-cart-plus ms-2"></i>
                            محاكاة إضافة للسلة
                        </button>
                        <button class="btn btn-danger" onclick="simulateWishlist()">
                            <i class="fas fa-heart ms-2"></i>
                            محاكاة المفضلة
                        </button>
                        <button class="btn btn-info" onclick="showDemoToast()">
                            <i class="fas fa-bell ms-2"></i>
                            إشعار تجريبي
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Test Links -->
            <div class="feature-demo">
                <h3 class="text-primary mb-4">
                    <i class="fas fa-link ms-2"></i>
                    روابط الاختبار
                </h3>
                
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-home ms-2"></i>
                                    الصفحة الرئيسية
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="card-text">اختبر التأثيرات الجديدة على المنتجات الحقيقية</p>
                                <a href="index.php" class="btn btn-primary w-100">
                                    <i class="fas fa-external-link-alt ms-2"></i>
                                    اختبار مباشر
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-box ms-2"></i>
                                    صفحة المنتج
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="card-text">اختبر معرض الصور المتحرك الجديد</p>
                                <a href="product.php?id=1" class="btn btn-success w-100">
                                    <i class="fas fa-images ms-2"></i>
                                    معرض الصور
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">
                                    <i class="fas fa-cogs ms-2"></i>
                                    الأدمن بانل
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="card-text">اختبر التحسينات في لوحة الإدارة</p>
                                <a href="admin/" class="btn btn-warning w-100">
                                    <i class="fas fa-tachometer-alt ms-2"></i>
                                    لوحة الإدارة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Demo functions
        function animateCard() {
            const card = document.querySelector('.demo-card .product-card');
            card.style.animation = 'bounceIn 0.8s ease-out';
            setTimeout(() => {
                card.style.animation = '';
            }, 800);
        }
        
        function simulateAddToCart() {
            const button = document.querySelector('.add-to-cart-btn');
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin ms-2"></i> جاري الإضافة...';
            button.disabled = true;
            
            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-check ms-2"></i> تم الإضافة!';
                button.classList.remove('add-to-cart-btn');
                button.classList.add('btn-success');
                
                showDemoToast('تم إضافة المنتج للسلة بنجاح!', 'success');
                
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('btn-success');
                    button.classList.add('add-to-cart-btn');
                    button.disabled = false;
                }, 2000);
            }, 1000);
        }
        
        function simulateWishlist() {
            const button = document.querySelector('.wishlist-btn');
            toggleWishlistDemo(button);
        }
        
        function toggleWishlistDemo(button) {
            const icon = button.querySelector('i');
            const isActive = button.classList.contains('active');
            
            if (isActive) {
                button.classList.remove('active');
                icon.classList.remove('fas');
                icon.classList.add('far');
                showDemoToast('تم إزالة المنتج من المفضلة', 'info');
            } else {
                button.classList.add('active');
                icon.classList.remove('far');
                icon.classList.add('fas');
                showDemoToast('تم إضافة المنتج للمفضلة', 'success');
                
                // Heart beat animation
                button.style.animation = 'heartBeat 0.6s ease';
                setTimeout(() => {
                    button.style.animation = '';
                }, 600);
            }
        }
        
        function quickViewDemo() {
            showDemoToast('سيتم فتح العرض السريع للمنتج', 'info');
        }
        
        function showDemoToast(message = 'هذا إشعار تجريبي!', type = 'info') {
            const toastContainer = document.getElementById('toast-container') || createToastContainer();
            
            const toast = document.createElement('div');
            toast.className = `toast show align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0`;
            toast.setAttribute('role', 'alert');
            
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} ms-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            toastContainer.appendChild(toast);
            
            // Auto remove after 3 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 3000);
        }
        
        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
            return container;
        }
        
        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-animate demo card
            setTimeout(() => {
                animateCard();
            }, 1000);
            
            // Show welcome toast
            setTimeout(() => {
                showDemoToast('مرحباً! جرب التأثيرات المختلفة', 'info');
            }, 500);
        });
    </script>
</body>
</html>
