<?php
/**
 * Fix All Images - إصلاح جميع الصور
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'classes/Product.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();
$product_class = new Product($db);

echo "<h1>إصلاح جميع مشاكل الصور</h1>";

$fixed_count = 0;
$error_count = 0;

// Step 1: Ensure directories exist
echo "<h2>1. إنشاء المجلدات المطلوبة</h2>";
$directories = [
    'assets/images/products',
    'assets/images/categories',
    'uploads/products',
    'uploads/categories'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0777, true)) {
            echo "✅ تم إنشاء مجلد: $dir<br>";
        } else {
            echo "❌ فشل في إنشاء مجلد: $dir<br>";
        }
    } else {
        echo "✅ مجلد موجود: $dir<br>";
    }
}

// Step 2: Copy images from uploads to assets
echo "<h2>2. نسخ الصور من uploads إلى assets</h2>";
if (is_dir('uploads/products')) {
    $uploaded_images = scandir('uploads/products');
    foreach ($uploaded_images as $image) {
        if (!in_array($image, ['.', '..']) && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $image)) {
            $source = 'uploads/products/' . $image;
            $destination = 'assets/images/products/' . $image;
            
            if (!file_exists($destination)) {
                if (copy($source, $destination)) {
                    echo "✅ نُسخت: $image<br>";
                    $fixed_count++;
                } else {
                    echo "❌ فشل في نسخ: $image<br>";
                    $error_count++;
                }
            } else {
                echo "⚠️ موجودة مسبقاً: $image<br>";
            }
        }
    }
}

// Step 3: Fix product primary images
echo "<h2>3. إصلاح الصور الرئيسية للمنتجات</h2>";
$query = "SELECT id, name, primary_image FROM products";
$stmt = $db->prepare($query);
$stmt->execute();
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($products as $product) {
    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
    echo "<strong>" . $product['name'] . "</strong><br>";
    
    if ($product['primary_image']) {
        $image_path = 'assets/images/products/' . $product['primary_image'];
        if (file_exists($image_path)) {
            echo "✅ الصورة الرئيسية موجودة: " . $product['primary_image'] . "<br>";
        } else {
            echo "❌ الصورة الرئيسية مفقودة: " . $product['primary_image'] . "<br>";
            
            // Try to find alternative image
            $images = $product_class->getProductImages($product['id']);
            if (!empty($images)) {
                $alt_image = $images[0]['image_path'];
                $alt_path = 'assets/images/products/' . $alt_image;
                
                if (file_exists($alt_path)) {
                    // Update primary image
                    $update_query = "UPDATE products SET primary_image = :image WHERE id = :id";
                    $update_stmt = $db->prepare($update_query);
                    $update_stmt->bindParam(':image', $alt_image);
                    $update_stmt->bindParam(':id', $product['id']);
                    
                    if ($update_stmt->execute()) {
                        echo "✅ تم تحديث الصورة الرئيسية إلى: $alt_image<br>";
                        $fixed_count++;
                    }
                }
            }
        }
    } else {
        echo "⚠️ لا توجد صورة رئيسية<br>";
        
        // Try to set first available image as primary
        $images = $product_class->getProductImages($product['id']);
        if (!empty($images)) {
            $first_image = $images[0]['image_path'];
            $first_path = 'assets/images/products/' . $first_image;
            
            if (file_exists($first_path)) {
                $update_query = "UPDATE products SET primary_image = :image WHERE id = :id";
                $update_stmt = $db->prepare($update_query);
                $update_stmt->bindParam(':image', $first_image);
                $update_stmt->bindParam(':id', $product['id']);
                
                if ($update_stmt->execute()) {
                    echo "✅ تم تحديد صورة رئيسية: $first_image<br>";
                    $fixed_count++;
                }
            }
        }
    }
    
    echo "</div>";
}

// Step 4: Test image URLs
echo "<h2>4. اختبار روابط الصور</h2>";
$test_products = array_slice($products, 0, 3);
foreach ($test_products as $product) {
    if ($product['primary_image']) {
        $image_url = ASSETS_URL . 'images/products/' . $product['primary_image'];
        echo "<div style='margin: 10px 0;'>";
        echo "<strong>" . $product['name'] . "</strong><br>";
        echo "الرابط: $image_url<br>";
        echo "<img src='$image_url' style='max-width: 150px; max-height: 150px; border: 1px solid #ccc;' alt='اختبار' onerror=\"this.style.display='none'; this.nextElementSibling.style.display='block';\">";
        echo "<div style='display: none; width: 150px; height: 150px; background: #f8f9fa; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center;'>❌ فشل التحميل</div>";
        echo "</div>";
    }
}

echo "<h2>ملخص الإصلاح</h2>";
echo "<div style='background: " . ($error_count > 0 ? '#f8d7da' : '#d4edda') . "; padding: 15px; border-radius: 5px;'>";
echo "عدد الإصلاحات: $fixed_count<br>";
echo "عدد الأخطاء: $error_count<br>";

if ($fixed_count > 0) {
    echo "<strong>✅ تم إصلاح $fixed_count عنصر بنجاح!</strong><br>";
}

if ($error_count > 0) {
    echo "<strong>⚠️ يوجد $error_count خطأ يحتاج لمراجعة يدوية</strong><br>";
}
echo "</div>";

echo "<br><div style='margin: 20px 0;'>";
echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>العودة للصفحة الرئيسية</a>";
echo "<a href='test_images.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار الصور</a>";
echo "</div>";
?>
