<?php
/**
 * Fix Product Images - إصلاح صور المنتجات
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'classes/Product.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();
$product_class = new Product($db);

echo "<h1>إصلاح صور المنتجات</h1>";

// Get all products
$query = "SELECT id, name, primary_image FROM products";
$stmt = $db->prepare($query);
$stmt->execute();
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h2>فحص المنتجات والصور</h2>";

$fixed_count = 0;
$error_count = 0;

foreach ($products as $product) {
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
    echo "<strong>المنتج: " . $product['name'] . " (ID: " . $product['id'] . ")</strong><br>";
    
    if ($product['primary_image']) {
        $old_path = 'uploads/products/' . $product['primary_image'];
        $new_path = 'assets/images/products/' . $product['primary_image'];
        
        echo "الصورة الحالية: " . $product['primary_image'] . "<br>";
        echo "المسار القديم: $old_path - " . (file_exists($old_path) ? '✅ موجود' : '❌ مفقود') . "<br>";
        echo "المسار الجديد: $new_path - " . (file_exists($new_path) ? '✅ موجود' : '❌ مفقود') . "<br>";
        
        // Copy from old to new if needed
        if (file_exists($old_path) && !file_exists($new_path)) {
            if (copy($old_path, $new_path)) {
                echo "✅ تم نسخ الصورة للمسار الجديد<br>";
                $fixed_count++;
            } else {
                echo "❌ فشل في نسخ الصورة<br>";
                $error_count++;
            }
        }
        
        // Test image display
        if (file_exists($new_path)) {
            $image_url = ASSETS_URL . 'images/products/' . $product['primary_image'];
            echo "معاينة: <img src='$image_url' style='width: 50px; height: 50px; object-fit: cover;' alt='معاينة'><br>";
        }
        
    } else {
        echo "❌ لا توجد صورة رئيسية<br>";
        
        // Check if there are images in product_images table
        $images = $product_class->getProductImages($product['id']);
        if (!empty($images)) {
            echo "توجد صور في جدول product_images: " . count($images) . " صورة<br>";
            
            // Set first image as primary
            $first_image = $images[0];
            $update_query = "UPDATE products SET primary_image = :image WHERE id = :id";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->bindParam(':image', $first_image['image_path']);
            $update_stmt->bindParam(':id', $product['id']);
            
            if ($update_stmt->execute()) {
                echo "✅ تم تحديد الصورة الرئيسية: " . $first_image['image_path'] . "<br>";
                $fixed_count++;
            } else {
                echo "❌ فشل في تحديد الصورة الرئيسية<br>";
                $error_count++;
            }
        }
    }
    
    echo "</div>";
}

echo "<h2>ملخص الإصلاح</h2>";
echo "عدد المنتجات المُصلحة: $fixed_count<br>";
echo "عدد الأخطاء: $error_count<br>";

if ($fixed_count > 0) {
    echo "<div style='background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ تم إصلاح $fixed_count منتج. يُنصح بإعادة تحميل الصفحة الرئيسية للتحقق من النتائج.";
    echo "</div>";
}

echo "<br><a href='index.php' class='btn btn-primary'>العودة للصفحة الرئيسية</a>";
echo " <a href='test_images.php' class='btn btn-info'>اختبار الصور</a>";
?>
