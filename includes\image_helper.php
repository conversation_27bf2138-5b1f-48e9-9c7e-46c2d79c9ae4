<?php
/**
 * Image Helper Functions - دوال مساعدة للصور
 */

/**
 * Get product image URL with enhanced fallback
 * الحصول على رابط صورة المنتج مع بديل محسن
 */
function get_product_image_url($image_name, $check_existence = true) {
    if (empty($image_name)) {
        return ASSETS_URL . 'images/placeholder.svg';
    }

    // Remove any leading slashes or paths
    $clean_image_name = basename($image_name);

    // Multiple possible paths to check
    $paths_to_check = [
        // Primary path: assets/images/products/
        [
            'path' => 'assets/images/products/' . $clean_image_name,
            'url' => ASSETS_URL . 'images/products/' . $clean_image_name
        ],
        // Alternative path: assets/images/
        [
            'path' => 'assets/images/' . $clean_image_name,
            'url' => ASSETS_URL . 'images/' . $clean_image_name
        ],
        // Fallback path: uploads/products/
        [
            'path' => 'uploads/products/' . $clean_image_name,
            'url' => BASE_URL . 'uploads/products/' . $clean_image_name
        ],
        // Legacy path: uploads/
        [
            'path' => 'uploads/' . $clean_image_name,
            'url' => BASE_URL . 'uploads/' . $clean_image_name
        ]
    ];

    if ($check_existence) {
        foreach ($paths_to_check as $path_info) {
            if (file_exists($path_info['path'])) {
                // If found in non-primary location, try to copy to primary
                if ($path_info['path'] !== 'assets/images/products/' . $clean_image_name) {
                    $primary_dir = 'assets/images/products/';
                    if (!file_exists($primary_dir)) {
                        mkdir($primary_dir, 0777, true);
                    }
                    $primary_path = $primary_dir . $clean_image_name;
                    if (!file_exists($primary_path) && copy($path_info['path'], $primary_path)) {
                        return ASSETS_URL . 'images/products/' . $clean_image_name;
                    }
                }
                return $path_info['url'];
            }
        }

        // If no image found, return placeholder
        return ASSETS_URL . 'images/placeholder.svg';
    }

    // Return primary URL without checking
    return ASSETS_URL . 'images/products/' . $clean_image_name;
}

/**
 * Get category image URL with fallback
 * الحصول على رابط صورة التصنيف مع بديل
 */
function get_category_image_url($image_name, $check_existence = true) {
    if (empty($image_name)) {
        return ASSETS_URL . 'images/placeholder.svg';
    }
    
    // Primary path: assets/images/categories/
    $primary_path = 'assets/images/categories/' . $image_name;
    $primary_url = ASSETS_URL . 'images/categories/' . $image_name;
    
    // Fallback path: uploads/categories/
    $fallback_path = 'uploads/categories/' . $image_name;
    $fallback_url = BASE_URL . 'uploads/categories/' . $image_name;
    
    if ($check_existence) {
        if (file_exists($primary_path)) {
            return $primary_url;
        } elseif (file_exists($fallback_path)) {
            // Copy to primary location for future use
            if (!file_exists('assets/images/categories/')) {
                mkdir('assets/images/categories/', 0777, true);
            }
            if (copy($fallback_path, $primary_path)) {
                return $primary_url;
            }
            return $fallback_url;
        } else {
            return ASSETS_URL . 'images/placeholder.svg';
        }
    }
    
    return $primary_url;
}

/**
 * Generate enhanced responsive image HTML
 * إنشاء HTML محسن للصورة المتجاوبة
 */
function generate_product_image_html($image_name, $alt_text, $css_classes = '', $style = '', $lazy_load = true) {
    $image_url = get_product_image_url($image_name);
    $placeholder_url = ASSETS_URL . 'images/placeholder.svg';

    $lazy_attr = $lazy_load ? 'loading="lazy"' : '';

    // Add default style if not provided
    if (empty($style)) {
        $style = 'height: 250px; object-fit: cover; width: 100%;';
    }

    // Enhanced error handling with multiple fallbacks
    $error_handler = "
        if (this.src !== '{$placeholder_url}') {
            this.src = '{$placeholder_url}';
            this.alt = 'صورة غير متوفرة';
            this.style.backgroundColor = '#f8f9fa';
        }
    ";

    return sprintf(
        '<img src="%s" class="%s" style="%s" alt="%s" onerror="%s" %s>',
        htmlspecialchars($image_url),
        htmlspecialchars($css_classes),
        htmlspecialchars($style),
        htmlspecialchars($alt_text),
        $error_handler,
        $lazy_attr
    );
}

/**
 * Generate category image HTML
 * إنشاء HTML لصورة التصنيف
 */
function generate_category_image_html($image_name, $alt_text, $css_classes = '', $style = '', $lazy_load = true) {
    $image_url = get_category_image_url($image_name);
    $placeholder_url = ASSETS_URL . 'images/placeholder.svg';
    
    $lazy_attr = $lazy_load ? 'loading="lazy"' : '';
    
    return sprintf(
        '<img src="%s" class="%s" style="%s" alt="%s" onerror="this.src=\'%s\'" %s>',
        htmlspecialchars($image_url),
        htmlspecialchars($css_classes),
        htmlspecialchars($style),
        htmlspecialchars($alt_text),
        $placeholder_url,
        $lazy_attr
    );
}

/**
 * Migrate image from uploads to assets
 * نقل الصورة من uploads إلى assets
 */
function migrate_product_image($image_name) {
    if (empty($image_name)) {
        return false;
    }
    
    $source = 'uploads/products/' . $image_name;
    $destination = 'assets/images/products/' . $image_name;
    
    if (file_exists($source) && !file_exists($destination)) {
        if (!file_exists('assets/images/products/')) {
            mkdir('assets/images/products/', 0777, true);
        }
        return copy($source, $destination);
    }
    
    return file_exists($destination);
}

/**
 * Clean up old image files
 * تنظيف ملفات الصور القديمة
 */
function cleanup_old_images() {
    $moved_count = 0;
    $error_count = 0;
    
    // Migrate product images
    if (is_dir('uploads/products/')) {
        $files = scandir('uploads/products/');
        foreach ($files as $file) {
            if (!in_array($file, ['.', '..']) && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $file)) {
                if (migrate_product_image($file)) {
                    $moved_count++;
                } else {
                    $error_count++;
                }
            }
        }
    }
    
    return ['moved' => $moved_count, 'errors' => $error_count];
}
?>
