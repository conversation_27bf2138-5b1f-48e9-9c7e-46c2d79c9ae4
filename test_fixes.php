<?php
/**
 * Test Fixes - اختبار الإصلاحات
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h1>اختبار الإصلاحات</h1>";

// Test 1: Currency functions
echo "<h2>1. اختبار دوال العملة</h2>";
echo "العملة الافتراضية: " . DEFAULT_CURRENCY . "<br>";
echo "العملة الحالية: " . get_current_currency() . "<br>";

// Test different currencies
$test_price = 299.99;
echo "<h3>اختبار تنسيق الأسعار:</h3>";
foreach (SUPPORTED_CURRENCIES as $code => $info) {
    echo $info['name'] . ": " . format_price($test_price, $code) . "<br>";
}

// Test 2: Image paths
echo "<h2>2. اختبار مسارات الصور</h2>";
echo "مسار صور المنتجات: " . PRODUCT_IMAGES_PATH . "<br>";
echo "رابط صور المنتجات: " . ASSETS_URL . "images/products/<br>";

// Check if directories exist
echo "<h3>فحص المجلدات:</h3>";
echo "مجلد assets/images/products موجود: " . (is_dir('assets/images/products') ? 'نعم' : 'لا') . "<br>";
echo "مجلد uploads/products موجود: " . (is_dir('uploads/products') ? 'نعم' : 'لا') . "<br>";
echo "مجلد uploads/categories موجود: " . (is_dir('uploads/categories') ? 'نعم' : 'لا') . "<br>";

// Test 3: Database connection
echo "<h2>3. اختبار الاتصال بقاعدة البيانات</h2>";
try {
    $database = new Database();
    $db = $database->getConnection();
    echo "الاتصال بقاعدة البيانات: نجح<br>";
    
    // Test Product class
    $product = new Product($db);
    echo "كلاس Product: يعمل<br>";
    
    // Test Category class
    $category = new Category($db);
    echo "كلاس Category: يعمل<br>";
    
} catch (Exception $e) {
    echo "خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

// Test 4: Check for required files
echo "<h2>4. فحص الملفات المطلوبة</h2>";
$required_files = [
    'admin/products/edit.php',
    'admin/categories/edit.php',
    'admin/settings.php',
    'includes/footer.php',
    'includes/change_currency.php',
    'api/cart.php'
];

foreach ($required_files as $file) {
    echo $file . ": " . (file_exists($file) ? 'موجود' : 'مفقود') . "<br>";
}

echo "<h2>✅ انتهى الاختبار</h2>";
echo '<a href="index.php" class="btn btn-primary">العودة للصفحة الرئيسية</a>';
?>
