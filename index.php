<?php
/**
 * Homepage - الصفحة الرئيسية
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/image_helper.php';
require_once 'classes/Product.php';
require_once 'classes/Category.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize classes
$product = new Product($db);
$product_class = $product; // Alias for consistency
$category = new Category($db);

// Get current currency
$current_currency = get_current_currency();

// Get featured products
$featured_products = $product->getFeaturedProducts(8);

// Get main categories
$main_categories = $category->getMainCategories();

// Get search parameters
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$category_id = isset($_GET['category']) ? (int)$_GET['category'] : null;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;

// Get products for main listing
$products = $product->getProducts($page, PRODUCTS_PER_PAGE, $category_id, $search);
$total_products = $product->getTotalCount($category_id, $search);
$total_pages = ceil($total_products / PRODUCTS_PER_PAGE);

// Page title
$page_title = SITE_NAME;
if ($search) {
    $page_title = "البحث عن: " . $search . " - " . SITE_NAME;
} elseif ($category_id) {
    $selected_category = $category->getCategoryById($category_id);
    if ($selected_category) {
        $page_title = $selected_category['name'] . " - " . SITE_NAME;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Swiper CSS -->
    <link href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo ASSETS_URL; ?>css/style.css" rel="stylesheet">
    
    <style>
        .hero-slider {
            height: 400px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        
        .product-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
        }
        
        .product-image {
            height: 250px;
            object-fit: cover;
        }
        
        .category-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .category-card:hover {
            transform: scale(1.05);
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>

    <!-- Enhanced Hero Slider -->
    <?php if (!$search && !$category_id): ?>
    <section class="hero-section">
        <div class="swiper hero-swiper">
            <div class="swiper-wrapper">
                <!-- Slide 1 -->
                <div class="swiper-slide hero-slide" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="container">
                        <div class="row align-items-center min-vh-50">
                            <div class="col-lg-6">
                                <div class="hero-content text-white">
                                    <h1 class="display-4 fw-bold mb-4 animate-slide-right">مرحباً بك في متجر شوبي</h1>
                                    <p class="lead mb-4 animate-slide-right" style="animation-delay: 0.2s;">اكتشف أفضل المنتجات بأسعار مميزة وجودة عالية</p>
                                    <div class="hero-buttons animate-slide-right" style="animation-delay: 0.4s;">
                                        <a href="#products" class="btn btn-light btn-lg me-3">
                                            <i class="fas fa-shopping-bag me-2"></i>
                                            تسوق الآن
                                        </a>
                                        <a href="#categories" class="btn btn-outline-light btn-lg">
                                            <i class="fas fa-list me-2"></i>
                                            التصنيفات
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="hero-image animate-slide-left">
                                    <img src="assets/images/hero-1.png" class="img-fluid" alt="تسوق الآن" style="max-height: 400px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 2 -->
                <div class="swiper-slide hero-slide" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <div class="container">
                        <div class="row align-items-center min-vh-50">
                            <div class="col-lg-6">
                                <div class="hero-content text-white">
                                    <h1 class="display-4 fw-bold mb-4 animate-slide-right">عروض حصرية</h1>
                                    <p class="lead mb-4 animate-slide-right" style="animation-delay: 0.2s;">خصومات تصل إلى 50% على مجموعة مختارة من المنتجات</p>
                                    <div class="hero-buttons animate-slide-right" style="animation-delay: 0.4s;">
                                        <a href="#featured" class="btn btn-light btn-lg me-3">
                                            <i class="fas fa-star me-2"></i>
                                            المنتجات المميزة
                                        </a>
                                        <a href="#offers" class="btn btn-outline-light btn-lg">
                                            <i class="fas fa-tags me-2"></i>
                                            العروض
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="hero-image animate-slide-left">
                                    <img src="assets/images/hero-2.png" class="img-fluid" alt="عروض حصرية" style="max-height: 400px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 3 -->
                <div class="swiper-slide hero-slide" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <div class="container">
                        <div class="row align-items-center min-vh-50">
                            <div class="col-lg-6">
                                <div class="hero-content text-white">
                                    <h1 class="display-4 fw-bold mb-4 animate-slide-right">توصيل مجاني</h1>
                                    <p class="lead mb-4 animate-slide-right" style="animation-delay: 0.2s;">توصيل مجاني لجميع الطلبات أكثر من 200 ريال</p>
                                    <div class="hero-buttons animate-slide-right" style="animation-delay: 0.4s;">
                                        <a href="#products" class="btn btn-light btn-lg me-3">
                                            <i class="fas fa-truck me-2"></i>
                                            اطلب الآن
                                        </a>
                                        <a href="#contact" class="btn btn-outline-light btn-lg">
                                            <i class="fas fa-phone me-2"></i>
                                            تواصل معنا
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="hero-image animate-slide-left">
                                    <img src="assets/images/hero-3.png" class="img-fluid" alt="توصيل مجاني" style="max-height: 400px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="swiper-button-next hero-next"></div>
            <div class="swiper-button-prev hero-prev"></div>

            <!-- Pagination -->
            <div class="swiper-pagination hero-pagination"></div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Categories Section -->
    <?php if (!$search && !$category_id && !empty($main_categories)): ?>
    <section class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5">تسوق حسب التصنيف</h2>
            <div class="row">
                <?php foreach (array_slice($main_categories, 0, 6) as $cat): ?>
                    <div class="col-lg-2 col-md-4 col-6 mb-4">
                        <div class="category-card text-center p-3 bg-white rounded shadow-sm h-100" 
                             onclick="location.href='?category=<?php echo $cat['id']; ?>'">
                            <?php if ($cat['image']): ?>
                                <img src="<?php echo ASSETS_URL; ?>images/categories/<?php echo $cat['image']; ?>" 
                                     class="img-fluid mb-3" style="height: 80px; object-fit: cover;" 
                                     alt="<?php echo $cat['name']; ?>">
                            <?php else: ?>
                                <i class="fas fa-folder fa-3x text-primary mb-3"></i>
                            <?php endif; ?>
                            <h6><?php echo $cat['name']; ?></h6>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Category Banners Section -->
    <?php if (!$search && !$category_id): ?>
    <section class="py-5">
        <div class="container">
            <div class="row g-4">
                <!-- Electronics Banner -->
                <div class="col-lg-4 col-md-6">
                    <div class="category-banner electronics-banner">
                        <div class="banner-content">
                            <div class="banner-icon">
                                <i class="fas fa-laptop"></i>
                            </div>
                            <h3>الإلكترونيات</h3>
                            <p>أحدث الأجهزة الذكية والتقنيات المتطورة</p>
                            <a href="?category=1" class="btn btn-banner">
                                تسوق الآن
                                <i class="fas fa-arrow-left ms-2"></i>
                            </a>
                        </div>
                        <div class="banner-image">
                            <img src="assets/images/banners/electronics.png" alt="الإلكترونيات">
                        </div>
                    </div>
                </div>

                <!-- Fashion Banner -->
                <div class="col-lg-4 col-md-6">
                    <div class="category-banner fashion-banner">
                        <div class="banner-content">
                            <div class="banner-icon">
                                <i class="fas fa-tshirt"></i>
                            </div>
                            <h3>الأزياء</h3>
                            <p>أناقة وموضة بأحدث التصاميم العصرية</p>
                            <a href="?category=2" class="btn btn-banner">
                                تسوق الآن
                                <i class="fas fa-arrow-left ms-2"></i>
                            </a>
                        </div>
                        <div class="banner-image">
                            <img src="assets/images/banners/fashion.png" alt="الأزياء">
                        </div>
                    </div>
                </div>

                <!-- Home Banner -->
                <div class="col-lg-4 col-md-12">
                    <div class="category-banner home-banner">
                        <div class="banner-content">
                            <div class="banner-icon">
                                <i class="fas fa-home"></i>
                            </div>
                            <h3>المنزل والحديقة</h3>
                            <p>كل ما تحتاجه لجعل منزلك أكثر جمالاً</p>
                            <a href="?category=3" class="btn btn-banner">
                                تسوق الآن
                                <i class="fas fa-arrow-left ms-2"></i>
                            </a>
                        </div>
                        <div class="banner-image">
                            <img src="assets/images/banners/home.png" alt="المنزل والحديقة">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Featured Products -->
    <?php if (!$search && !$category_id && !empty($featured_products)): ?>
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-6 fw-bold text-primary mb-3">
                    <i class="fas fa-star me-2"></i>
                    المنتجات المميزة
                </h2>
                <p class="text-muted">اكتشف أفضل منتجاتنا المختارة بعناية</p>
            </div>
            <div class="row">
                <?php foreach ($featured_products as $product_item): ?>
                    <?php
                    // Get all images for this featured product
                    $product_images = $product_class->getProductImages($product_item['id']);
                    ?>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card product-card h-100">
                            <!-- Enhanced Image Container with Slider -->
                            <div class="product-image-container">
                                <?php if (!empty($product_images) && count($product_images) > 1): ?>
                                    <!-- Image Slider for Multiple Images -->
                                    <div class="product-image-slider">
                                        <div class="swiper product-card-swiper" data-product-id="<?php echo $product_item['id']; ?>">
                                            <div class="swiper-wrapper">
                                                <?php foreach ($product_images as $image): ?>
                                                    <div class="swiper-slide">
                                                        <?php echo generate_product_image_html(
                                                            $image['image_path'],
                                                            $product_item['name'],
                                                            'card-img-top product-image'
                                                        ); ?>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                            <!-- Navigation buttons -->
                                            <div class="swiper-button-next product-slider-next"></div>
                                            <div class="swiper-button-prev product-slider-prev"></div>
                                            <!-- Pagination dots -->
                                            <div class="swiper-pagination product-slider-pagination"></div>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <!-- Single Image -->
                                    <?php echo generate_product_image_html(
                                        $product_item['primary_image'],
                                        $product_item['name'],
                                        'card-img-top product-image'
                                    ); ?>
                                <?php endif; ?>

                                <!-- Sale Badge -->
                                <?php if ($product_item['sale_price']): ?>
                                    <div class="sale-badge">
                                        <i class="fas fa-tag me-1"></i>
                                        خصم <?php echo round((($product_item['price'] - $product_item['sale_price']) / $product_item['price']) * 100); ?>%
                                    </div>
                                <?php endif; ?>

                                <!-- Featured Badge -->
                                <div class="featured-badge">
                                    <i class="fas fa-star me-1"></i>
                                    مميز
                                </div>

                                <!-- Enhanced Wishlist Button -->
                                <button class="wishlist-btn" onclick="toggleWishlist(<?php echo $product_item['id']; ?>, this)"
                                        title="إضافة للمفضلة">
                                    <i class="far fa-heart"></i>
                                </button>
                            </div>

                            <div class="card-body d-flex flex-column product-card-body">
                                <h6 class="card-title product-title"><?php echo $product_item['name']; ?></h6>
                                <p class="card-text text-muted small flex-grow-1">
                                    <?php echo substr($product_item['short_description'] ?: $product_item['description'], 0, 80); ?>...
                                </p>

                                <!-- Rating -->
                                <div class="rating mb-2">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="fas fa-star <?php echo $i <= 4 ? 'text-warning' : 'text-muted'; ?>"></i>
                                    <?php endfor; ?>
                                    <small class="text-muted ms-2">(4.0)</small>
                                </div>

                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div class="price product-price">
                                            <?php if ($product_item['sale_price']): ?>
                                                <span class="text-danger fw-bold">
                                                    <?php echo format_price($product_item['sale_price'], $current_currency); ?>
                                                </span>
                                                <small class="text-muted text-decoration-line-through ms-2">
                                                    <?php echo format_price($product_item['price'], $current_currency); ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="text-primary fw-bold">
                                                    <?php echo format_price($product_item['price'], $current_currency); ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Stock Status -->
                                        <div class="stock-status">
                                            <?php if ($product_item['stock_quantity'] > 10): ?>
                                                <small class="text-success">
                                                    <i class="fas fa-check-circle ms-1"></i>
                                                    متوفر
                                                </small>
                                            <?php elseif ($product_item['stock_quantity'] > 0): ?>
                                                <small class="text-warning">
                                                    <i class="fas fa-exclamation-triangle ms-1"></i>
                                                    كمية محدودة
                                                </small>
                                            <?php else: ?>
                                                <small class="text-danger">
                                                    <i class="fas fa-times-circle ms-1"></i>
                                                    غير متوفر
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <?php if ($product_item['stock_quantity'] > 0): ?>
                                            <button type="button" class="btn add-to-cart-btn w-100"
                                                    onclick="addToCartQuick(<?php echo $product_item['id']; ?>)">
                                                <i class="fas fa-shopping-cart ms-2"></i>
                                                أضف للسلة
                                            </button>
                                        <?php else: ?>
                                            <button type="button" class="btn btn-secondary w-100" disabled>
                                                <i class="fas fa-ban ms-2"></i>
                                                غير متوفر
                                            </button>
                                        <?php endif; ?>

                                        <a href="product.php?id=<?php echo $product_item['id']; ?>"
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye ms-2"></i>
                                            عرض التفاصيل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Products Listing -->
    <section class="py-5" id="products">
        <div class="container">
            <?php if ($search || $category_id): ?>
                <div class="row mb-4">
                    <div class="col">
                        <h2>
                            <?php if ($search): ?>
                                نتائج البحث عن: "<?php echo htmlspecialchars($search); ?>"
                            <?php elseif ($category_id && isset($selected_category)): ?>
                                <?php echo $selected_category['name']; ?>
                            <?php endif; ?>
                            <small class="text-muted">(<?php echo $total_products; ?> منتج)</small>
                        </h2>
                    </div>
                </div>
            <?php elseif (!empty($products)): ?>
                <h2 class="text-center mb-5">جميع المنتجات</h2>
            <?php endif; ?>

            <?php if (!empty($products)): ?>
                <div class="row">
                    <?php foreach ($products as $product_item): ?>
                        <?php
                        // Get all images for this product
                        $product_images = $product_class->getProductImages($product_item['id']);
                        ?>
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card product-card h-100">
                                <!-- Enhanced Image Container with Slider -->
                                <div class="product-image-container">
                                    <?php if (!empty($product_images) && count($product_images) > 1): ?>
                                        <!-- Image Slider for Multiple Images -->
                                        <div class="product-image-slider">
                                            <div class="swiper product-card-swiper" data-product-id="<?php echo $product_item['id']; ?>">
                                                <div class="swiper-wrapper">
                                                    <?php foreach ($product_images as $image): ?>
                                                        <div class="swiper-slide">
                                                            <?php echo generate_product_image_html(
                                                                $image['image_path'],
                                                                $product_item['name'],
                                                                'card-img-top product-image'
                                                            ); ?>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                                <!-- Navigation buttons -->
                                                <div class="swiper-button-next product-slider-next"></div>
                                                <div class="swiper-button-prev product-slider-prev"></div>
                                                <!-- Pagination dots -->
                                                <div class="swiper-pagination product-slider-pagination"></div>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <!-- Single Image -->
                                        <?php echo generate_product_image_html(
                                            $product_item['primary_image'],
                                            $product_item['name'],
                                            'card-img-top product-image'
                                        ); ?>
                                    <?php endif; ?>

                                    <!-- Sale Badge -->
                                    <?php if ($product_item['sale_price']): ?>
                                        <div class="sale-badge">
                                            <i class="fas fa-tag me-1"></i>
                                            خصم <?php echo round((($product_item['price'] - $product_item['sale_price']) / $product_item['price']) * 100); ?>%
                                        </div>
                                    <?php endif; ?>

                                    <!-- Enhanced Wishlist Button -->
                                    <button class="wishlist-btn" onclick="toggleWishlist(<?php echo $product_item['id']; ?>, this)"
                                            title="إضافة للمفضلة">
                                        <i class="far fa-heart"></i>
                                    </button>


                                </div>
                                
                                <div class="card-body d-flex flex-column product-card-body">
                                    <h6 class="card-title product-title"><?php echo $product_item['name']; ?></h6>
                                    <p class="card-text text-muted small flex-grow-1">
                                        <?php echo substr($product_item['short_description'], 0, 80); ?>...
                                    </p>

                                    <!-- Rating -->
                                    <div class="rating mb-2">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star <?php echo $i <= 4 ? 'text-warning' : 'text-muted'; ?>"></i>
                                        <?php endfor; ?>
                                        <small class="text-muted ms-2">(4.0)</small>
                                    </div>

                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div class="price product-price">
                                                <?php if ($product_item['sale_price']): ?>
                                                    <span class="text-danger fw-bold"><?php echo format_price($product_item['sale_price'], $current_currency); ?></span>
                                                    <small class="text-muted text-decoration-line-through ms-2">
                                                        <?php echo format_price($product_item['price'], $current_currency); ?>
                                                    </small>
                                                <?php else: ?>
                                                    <span class="text-primary fw-bold"><?php echo format_price($product_item['price'], $current_currency); ?></span>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Stock Status -->
                                            <div class="stock-status">
                                                <?php if ($product_item['stock_quantity'] > 10): ?>
                                                    <small class="text-success">
                                                        <i class="fas fa-check-circle ms-1"></i>
                                                        متوفر
                                                    </small>
                                                <?php elseif ($product_item['stock_quantity'] > 0): ?>
                                                    <small class="text-warning">
                                                        <i class="fas fa-exclamation-triangle ms-1"></i>
                                                        كمية محدودة
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-danger">
                                                        <i class="fas fa-times-circle ms-1"></i>
                                                        غير متوفر
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="d-grid gap-2">
                                            <?php if ($product_item['stock_quantity'] > 0): ?>
                                                <button type="button" class="btn add-to-cart-btn w-100"
                                                        onclick="addToCartQuick(<?php echo $product_item['id']; ?>)">
                                                    <i class="fas fa-shopping-cart ms-2"></i>
                                                    أضف للسلة
                                                </button>
                                            <?php else: ?>
                                                <button type="button" class="btn btn-secondary w-100" disabled>
                                                    <i class="fas fa-ban ms-2"></i>
                                                    غير متوفر
                                                </button>
                                            <?php endif; ?>

                                            <a href="product.php?id=<?php echo $product_item['id']; ?>"
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye ms-2"></i>
                                                عرض التفاصيل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="row mt-5">
                        <div class="col">
                            <?php 
                            $base_url = '?';
                            if ($search) $base_url .= 'search=' . urlencode($search) . '&';
                            if ($category_id) $base_url .= 'category=' . $category_id . '&';
                            echo generate_pagination($page, $total_pages, rtrim($base_url, '&'));
                            ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>لا توجد منتجات</h4>
                    <p class="text-muted">لم يتم العثور على منتجات تطابق البحث</p>
                    <a href="<?php echo BASE_URL; ?>" class="btn btn-primary">العودة للرئيسية</a>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <?php include 'includes/footer.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Enhanced page initialization - SIMPLIFIED
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize hero slider
            initializeHeroSlider();

            // Initialize product card sliders
            initializeProductCardSliders();

            // Initialize search functionality
            const searchForm = document.querySelector('.search-form');
            if (searchForm) {
                searchForm.addEventListener('submit', function(e) {
                    const searchInput = this.querySelector('input[name="search"]');
                    if (!searchInput.value.trim()) {
                        e.preventDefault();
                        searchInput.focus();
                    }
                });
            }

            // Initialize category filters
            document.querySelectorAll('.category-filter').forEach(filter => {
                filter.addEventListener('click', function() {
                    const categoryId = this.dataset.categoryId;
                    window.location.href = `?category=${categoryId}`;
                });
            });

            console.log('Page initialized successfully');
        });

        // Initialize Hero Slider
        function initializeHeroSlider() {
            const heroSwiper = document.querySelector('.hero-swiper');
            if (heroSwiper) {
                new Swiper('.hero-swiper', {
                    slidesPerView: 1,
                    spaceBetween: 0,
                    loop: true,
                    autoplay: {
                        delay: 5000,
                        disableOnInteraction: false,
                    },
                    navigation: {
                        nextEl: '.hero-next',
                        prevEl: '.hero-prev',
                    },
                    pagination: {
                        el: '.hero-pagination',
                        clickable: true,
                        dynamicBullets: true,
                    },
                    effect: 'fade',
                    fadeEffect: {
                        crossFade: true
                    },
                    speed: 1000,
                    on: {
                        slideChange: function() {
                            // Reset animations on slide change
                            const currentSlide = this.slides[this.activeIndex];
                            const animatedElements = currentSlide.querySelectorAll('.animate-slide-right, .animate-slide-left');

                            animatedElements.forEach(el => {
                                el.style.animation = 'none';
                                setTimeout(() => {
                                    el.style.animation = '';
                                }, 10);
                            });
                        }
                    }
                });
            }
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Quick add to cart function
        function addToCartQuick(productId) {
            const button = event.target;
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري...';
            button.disabled = true;

            // Send AJAX request
            fetch('api/cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=add&product_id=${productId}&quantity=1`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success state
                    button.innerHTML = '<i class="fas fa-check"></i> تم';
                    button.className = 'btn btn-success btn-sm w-100';

                    // Update cart count in navbar
                    updateCartCount();

                    // Show notification
                    showNotification('تم إضافة المنتج إلى السلة بنجاح!', 'success');

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.className = 'btn btn-primary btn-sm w-100';
                        button.disabled = false;
                    }, 2000);
                } else {
                    // Show error
                    showNotification(data.message || 'حدث خطأ أثناء إضافة المنتج', 'error');
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('حدث خطأ في الاتصال', 'error');
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }

        // Update cart count in navbar
        function updateCartCount() {
            fetch('api/cart.php?action=get_count')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const cartBadge = document.querySelector('.navbar .badge');
                    if (data.count > 0) {
                        if (cartBadge) {
                            cartBadge.textContent = data.count;
                        } else {
                            // Create badge if it doesn't exist
                            const cartLink = document.querySelector('.navbar .fa-shopping-cart').parentElement;
                            const badge = document.createElement('span');
                            badge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
                            badge.textContent = data.count;
                            cartLink.appendChild(badge);
                        }
                    } else if (cartBadge) {
                        cartBadge.remove();
                    }
                }
            })
            .catch(error => console.error('Error updating cart count:', error));
        }

        // Simple notification function
        function showNotification(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'info': 'alert-info'
            };

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass[type]} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }
    </script>

    <!-- Custom JS -->
    <script src="<?php echo ASSETS_URL; ?>js/script.js"></script>

    <!-- Enhanced Product Interactions -->
    <script>
        // Animate cards on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationDelay = Math.random() * 0.3 + 's';
                    entry.target.classList.add('animate-slide-up');
                }
            });
        }, observerOptions);



        // Enhanced Toggle wishlist
        function toggleWishlist(productId, button) {
            const icon = button.querySelector('i');
            const isActive = button.classList.contains('active');

            // Add loading state
            button.style.pointerEvents = 'none';
            icon.className = 'fas fa-spinner fa-spin';

            // Simulate API call (replace with actual AJAX)
            setTimeout(() => {
                if (isActive) {
                    button.classList.remove('active');
                    icon.classList.remove('fas', 'fa-spinner', 'fa-spin');
                    icon.classList.add('far', 'fa-heart');
                    showToast('تم إزالة المنتج من المفضلة', 'info');
                } else {
                    button.classList.add('active');
                    icon.classList.remove('far', 'fa-spinner', 'fa-spin');
                    icon.classList.add('fas', 'fa-heart');
                    showToast('تم إضافة المنتج للمفضلة', 'success');

                    // Add heart animation
                    button.style.animation = 'heartBeat 0.8s ease';
                    setTimeout(() => {
                        button.style.animation = '';
                    }, 800);
                }

                button.style.pointerEvents = 'auto';
            }, 500);

            // Here you would typically send AJAX request to save wishlist state
            /*
            fetch('api/wishlist.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=${isActive ? 'remove' : 'add'}&product_id=${productId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Handle success
                } else {
                    // Handle error
                    showToast('حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
                }
            })
            .catch(error => {
                showToast('حدث خطأ في الاتصال', 'error');
            });
            */
        }

        // Enhanced Add to Cart Quick
        function addToCartQuick(productId) {
            const button = event.target.closest('button');
            const originalText = button.innerHTML;

            // Add loading state
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';

            // Simulate API call (replace with actual AJAX)
            setTimeout(() => {
                showToast('تم إضافة المنتج للسلة بنجاح', 'success');

                // Reset button
                button.disabled = false;
                button.innerHTML = originalText;

                // Add success animation
                button.style.animation = 'bounceIn 0.6s ease';
                setTimeout(() => {
                    button.style.animation = '';
                }, 600);

                // Update cart count (if you have a cart counter)
                updateCartCount();
            }, 800);

            // Here you would typically send AJAX request
            /*
            fetch('api/cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=add&product_id=${productId}&quantity=1`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('تم إضافة المنتج للسلة بنجاح', 'success');
                    updateCartCount();
                } else {
                    showToast(data.message || 'حدث خطأ، يرجى المحاولة مرة أخرى', 'error');
                }
            })
            .catch(error => {
                showToast('حدث خطأ في الاتصال', 'error');
            })
            .finally(() => {
                button.disabled = false;
                button.innerHTML = originalText;
            });
            */
        }

        // Update cart count
        function updateCartCount() {
            // This would typically fetch the current cart count
            const cartBadge = document.querySelector('.cart-count');
            if (cartBadge) {
                let currentCount = parseInt(cartBadge.textContent) || 0;
                cartBadge.textContent = currentCount + 1;
                cartBadge.style.animation = 'bounceIn 0.6s ease';
                setTimeout(() => {
                    cartBadge.style.animation = '';
                }, 600);
            }
        }



        // Enhanced add to cart with animation
        function addToCartQuick(productId) {
            const button = event.target;
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin ms-2"></i> جاري الإضافة...';
            button.disabled = true;

            fetch('api/cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=add&product_id=${productId}&quantity=1`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Success animation
                    button.innerHTML = '<i class="fas fa-check ms-2"></i> تم الإضافة!';
                    button.classList.remove('add-to-cart-btn');
                    button.classList.add('btn-success');

                    // Show success toast
                    showToast('تم إضافة المنتج للسلة بنجاح', 'success');

                    // Update cart count
                    updateCartCount();

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.classList.remove('btn-success');
                        button.classList.add('add-to-cart-btn');
                        button.disabled = false;
                    }, 2000);

                } else {
                    button.innerHTML = originalText;
                    button.disabled = false;
                    showToast('حدث خطأ: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                button.innerHTML = originalText;
                button.disabled = false;
                showToast('حدث خطأ في الشبكة', 'error');
            });
        }

        // Toast notification system
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toast-container') || createToastContainer();

            const toast = document.createElement('div');
            toast.className = `toast show align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0`;
            toast.setAttribute('role', 'alert');

            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} ms-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            toastContainer.appendChild(toast);

            // Auto remove after 3 seconds
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
            return container;
        }

        // Heart beat animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes heartBeat {
                0% { transform: scale(1); }
                14% { transform: scale(1.3); }
                28% { transform: scale(1); }
                42% { transform: scale(1.3); }
                70% { transform: scale(1); }
            }

            @keyframes bounceIn {
                0% {
                    opacity: 0;
                    transform: scale(0.3);
                }
                50% {
                    opacity: 1;
                    transform: scale(1.05);
                }
                70% {
                    transform: scale(0.9);
                }
                100% {
                    opacity: 1;
                    transform: scale(1);
                }
            }
        `;
        document.head.appendChild(style);



        // Initialize Product Card Sliders
        function initializeProductCardSliders() {
            document.querySelectorAll('.product-card-swiper').forEach((swiperEl, index) => {
                const productId = swiperEl.dataset.productId;

                new Swiper(swiperEl, {
                    slidesPerView: 1,
                    spaceBetween: 0,
                    loop: true,
                    autoplay: {
                        delay: 4000,
                        disableOnInteraction: false,
                        pauseOnMouseEnter: true,
                    },
                    navigation: {
                        nextEl: swiperEl.querySelector('.product-slider-next'),
                        prevEl: swiperEl.querySelector('.product-slider-prev'),
                    },
                    pagination: {
                        el: swiperEl.querySelector('.product-slider-pagination'),
                        clickable: true,
                        dynamicBullets: true,
                    },
                    effect: 'fade',
                    fadeEffect: {
                        crossFade: true
                    },
                    speed: 600,
                    on: {
                        init: function() {
                            // Add slight delay for staggered initialization
                            this.autoplay.delay = 4000 + (index * 500);
                        }
                    }
                });
            });
        }
    </script>
</body>
</html>
