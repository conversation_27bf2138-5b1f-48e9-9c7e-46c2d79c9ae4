<?php
/**
 * Product Class
 * فئة المنتجات
 */

class Product {
    private $conn;
    private $table_name = "products";

    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all products with pagination
     * الحصول على جميع المنتجات مع الترقيم
     */
    public function getProducts($page = 1, $limit = PRODUCTS_PER_PAGE, $category_id = null, $search = null) {
        $offset = ($page - 1) * $limit;
        
        $query = "SELECT p.*, c.name as category_name, 
                         (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
                  FROM " . $this->table_name . " p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  WHERE p.is_active = 1";
        
        if ($category_id) {
            $query .= " AND p.category_id = :category_id";
        }
        
        if ($search) {
            $query .= " AND (p.name LIKE :search OR p.description LIKE :search)";
        }
        
        $query .= " ORDER BY p.created_at DESC LIMIT :limit OFFSET :offset";
        
        $stmt = $this->conn->prepare($query);
        
        if ($category_id) {
            $stmt->bindParam(':category_id', $category_id);
        }
        
        if ($search) {
            $search_term = "%{$search}%";
            $stmt->bindParam(':search', $search_term);
        }
        
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get total products count
     * الحصول على العدد الإجمالي للمنتجات
     */
    public function getTotalCount($category_id = null, $search = null) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE is_active = 1";
        
        if ($category_id) {
            $query .= " AND category_id = :category_id";
        }
        
        if ($search) {
            $query .= " AND (name LIKE :search OR description LIKE :search)";
        }
        
        $stmt = $this->conn->prepare($query);
        
        if ($category_id) {
            $stmt->bindParam(':category_id', $category_id);
        }
        
        if ($search) {
            $search_term = "%{$search}%";
            $stmt->bindParam(':search', $search_term);
        }
        
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'];
    }

    /**
     * Get product by ID
     * الحصول على منتج بالمعرف
     */
    public function getProductById($id) {
        $query = "SELECT p.*, c.name as category_name 
                  FROM " . $this->table_name . " p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  WHERE p.id = :id AND p.is_active = 1";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get product images
     * الحصول على صور المنتج
     */
    public function getProductImages($product_id) {
        $query = "SELECT * FROM product_images WHERE product_id = :product_id ORDER BY sort_order, is_primary DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get product variants
     * الحصول على متغيرات المنتج
     */
    public function getProductVariants($product_id) {
        $query = "SELECT * FROM product_variants
                  WHERE product_id = :product_id
                  ORDER BY id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get featured products
     * الحصول على المنتجات المميزة
     */
    public function getFeaturedProducts($limit = 8) {
        $query = "SELECT p.*, c.name as category_name,
                         (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
                  FROM " . $this->table_name . " p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  WHERE p.is_active = 1 AND p.is_featured = 1
                  ORDER BY p.created_at DESC 
                  LIMIT :limit";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get related products
     * الحصول على المنتجات ذات الصلة
     */
    public function getRelatedProducts($product_id, $category_id, $limit = 4) {
        $query = "SELECT p.*, c.name as category_name,
                         (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
                  FROM " . $this->table_name . " p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  WHERE p.is_active = 1 AND p.category_id = :category_id AND p.id != :product_id
                  ORDER BY RAND() 
                  LIMIT :limit";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':category_id', $category_id);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Create new product
     * إنشاء منتج جديد
     */
    public function createProduct($data) {
        $query = "INSERT INTO " . $this->table_name . " 
                  (name, description, short_description, sku, price, sale_price, stock_quantity, 
                   category_id, weight, dimensions, is_featured, meta_title, meta_description) 
                  VALUES (:name, :description, :short_description, :sku, :price, :sale_price, 
                          :stock_quantity, :category_id, :weight, :dimensions, :is_featured, 
                          :meta_title, :meta_description)";
        
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':short_description', $data['short_description']);
        $stmt->bindParam(':sku', $data['sku']);
        $stmt->bindParam(':price', $data['price']);
        $stmt->bindParam(':sale_price', $data['sale_price']);
        $stmt->bindParam(':stock_quantity', $data['stock_quantity']);
        $stmt->bindParam(':category_id', $data['category_id']);
        $stmt->bindParam(':weight', $data['weight']);
        $stmt->bindParam(':dimensions', $data['dimensions']);
        $stmt->bindParam(':is_featured', $data['is_featured']);
        $stmt->bindParam(':meta_title', $data['meta_title']);
        $stmt->bindParam(':meta_description', $data['meta_description']);
        
        if ($stmt->execute()) {
            return $this->conn->lastInsertId();
        }
        
        return false;
    }



    /**
     * Delete product
     * حذف منتج
     */
    public function deleteProduct($id) {
        $query = "UPDATE " . $this->table_name . " SET is_active = 0 WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        
        return $stmt->execute();
    }

    /**
     * Add product image
     * إضافة صورة منتج
     */
    public function addProductImage($product_id, $image_path, $alt_text = '', $is_primary = false) {
        // If this is primary image, unset other primary images
        if ($is_primary) {
            $update_query = "UPDATE product_images SET is_primary = 0 WHERE product_id = :product_id";
            $update_stmt = $this->conn->prepare($update_query);
            $update_stmt->bindParam(':product_id', $product_id);
            $update_stmt->execute();
        }

        $query = "INSERT INTO product_images (product_id, image_path, alt_text, is_primary)
                  VALUES (:product_id, :image_path, :alt_text, :is_primary)";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->bindParam(':image_path', $image_path);
        $stmt->bindParam(':alt_text', $alt_text);
        $stmt->bindParam(':is_primary', $is_primary);

        return $stmt->execute();
    }

    /**
     * Update product
     * تحديث منتج
     */
    public function updateProduct($id, $data) {
        $query = "UPDATE " . $this->table_name . " SET
                  name = :name,
                  description = :description,
                  short_description = :short_description,
                  sku = :sku,
                  price = :price,
                  sale_price = :sale_price,
                  stock_quantity = :stock_quantity,
                  category_id = :category_id,
                  weight = :weight,
                  dimensions = :dimensions,
                  is_featured = :is_featured,
                  meta_title = :meta_title,
                  meta_description = :meta_description,
                  updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':short_description', $data['short_description']);
        $stmt->bindParam(':sku', $data['sku']);
        $stmt->bindParam(':price', $data['price']);
        $stmt->bindParam(':sale_price', $data['sale_price']);
        $stmt->bindParam(':stock_quantity', $data['stock_quantity']);
        $stmt->bindParam(':category_id', $data['category_id']);
        $stmt->bindParam(':weight', $data['weight']);
        $stmt->bindParam(':dimensions', $data['dimensions']);
        $stmt->bindParam(':is_featured', $data['is_featured'], PDO::PARAM_BOOL);
        $stmt->bindParam(':meta_title', $data['meta_title']);
        $stmt->bindParam(':meta_description', $data['meta_description']);

        return $stmt->execute();
    }

    /**
     * Delete product image
     * حذف صورة منتج
     */
    public function deleteProductImage($image_id) {
        // Get image info first
        $query = "SELECT image_path FROM product_images WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $image_id);
        $stmt->execute();
        $image = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($image) {
            // Delete from database
            $delete_query = "DELETE FROM product_images WHERE id = :id";
            $delete_stmt = $this->conn->prepare($delete_query);
            $delete_stmt->bindParam(':id', $image_id);

            if ($delete_stmt->execute()) {
                // Delete physical file from assets directory
                $file_path = PRODUCT_IMAGES_PATH . $image['image_path'];
                if (file_exists($file_path)) {
                    unlink($file_path);
                }
                return true;
            }
        }

        return false;
    }

    /**
     * Set primary image
     * تحديد الصورة الرئيسية
     */
    public function setPrimaryImage($product_id, $image_id) {
        try {
            $this->conn->beginTransaction();

            // Unset all primary images for this product
            $update_query = "UPDATE product_images SET is_primary = 0 WHERE product_id = :product_id";
            $update_stmt = $this->conn->prepare($update_query);
            $update_stmt->bindParam(':product_id', $product_id);
            $update_stmt->execute();

            // Set the selected image as primary
            $primary_query = "UPDATE product_images SET is_primary = 1 WHERE id = :id AND product_id = :product_id";
            $primary_stmt = $this->conn->prepare($primary_query);
            $primary_stmt->bindParam(':id', $image_id);
            $primary_stmt->bindParam(':product_id', $product_id);
            $primary_stmt->execute();

            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }
}
?>
