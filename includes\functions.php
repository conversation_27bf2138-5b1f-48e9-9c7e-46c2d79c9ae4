<?php
/**
 * Common Functions
 * الدوال المشتركة
 */

/**
 * Sanitize input data
 * تنظيف البيانات المدخلة
 */
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Check if user is logged in
 * التحقق من تسجيل دخول المستخدم
 */
function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Check if user is admin
 * التحقق من صلاحيات الإدارة
 */
function is_admin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Redirect to login if not authenticated
 * إعادة توجيه لصفحة تسجيل الدخول
 */
function require_login() {
    if (!is_logged_in()) {
        header('Location: ' . BASE_URL . 'auth/login.php');
        exit();
    }
}

/**
 * Require admin privileges
 * طلب صلاحيات الإدارة
 */
function require_admin() {
    require_login();
    if (!is_admin()) {
        header('Location: ' . BASE_URL . 'index.php');
        exit();
    }
}

/**
 * Format price with currency
 * تنسيق السعر مع العملة
 */
function format_price($price, $currency = null) {
    if ($currency === null) {
        $currency = DEFAULT_CURRENCY;
    }

    $currencies = SUPPORTED_CURRENCIES;
    $symbol = isset($currencies[$currency]) ? $currencies[$currency]['symbol'] : 'ر.س';
    return number_format($price, 2) . ' ' . $symbol;
}

/**
 * Get current currency from session or default
 * الحصول على العملة الحالية من الجلسة أو الافتراضية
 */
function get_current_currency() {
    return isset($_SESSION['currency']) ? $_SESSION['currency'] : DEFAULT_CURRENCY;
}

/**
 * Set current currency in session
 * تحديد العملة الحالية في الجلسة
 */
function set_current_currency($currency) {
    if (array_key_exists($currency, SUPPORTED_CURRENCIES)) {
        $_SESSION['currency'] = $currency;
        return true;
    }
    return false;
}

/**
 * Generate unique order number
 * إنشاء رقم طلب فريد
 */
function generate_order_number() {
    return 'ORD-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
}

/**
 * Upload image file
 * رفع ملف صورة
 */
function upload_image($file, $upload_dir) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return false;
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, ALLOWED_IMAGE_TYPES)) {
        return false;
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return false;
    }
    
    $new_filename = uniqid() . '.' . $file_extension;
    $upload_path = $upload_dir . $new_filename;
    
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return $new_filename;
    }
    
    return false;
}

/**
 * Delete image file
 * حذف ملف صورة
 */
function delete_image($filename, $upload_dir) {
    $file_path = $upload_dir . $filename;
    if (file_exists($file_path)) {
        return unlink($file_path);
    }
    return false;
}

/**
 * Get cart item count
 * الحصول على عدد عناصر السلة
 */
function get_cart_count() {
    if (!isset($_SESSION['cart'])) {
        return 0;
    }
    return array_sum($_SESSION['cart']);
}

/**
 * Add product to cart
 * إضافة منتج للسلة
 */
function add_to_cart($product_id, $variant_id = null, $quantity = 1) {
    $cart_key = $product_id . ($variant_id ? '_' . $variant_id : '');
    
    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = array();
    }
    
    if (isset($_SESSION['cart'][$cart_key])) {
        $_SESSION['cart'][$cart_key] += $quantity;
    } else {
        $_SESSION['cart'][$cart_key] = $quantity;
    }
    
    return true;
}

/**
 * Remove product from cart
 * إزالة منتج من السلة
 */
function remove_from_cart($product_id, $variant_id = null) {
    $cart_key = $product_id . ($variant_id ? '_' . $variant_id : '');
    
    if (isset($_SESSION['cart'][$cart_key])) {
        unset($_SESSION['cart'][$cart_key]);
        return true;
    }
    
    return false;
}

/**
 * Clear cart
 * مسح السلة
 */
function clear_cart() {
    unset($_SESSION['cart']);
}

/**
 * Send Telegram notification
 * إرسال إشعار تليجرام
 */
function send_telegram_notification($message) {
    if (empty(TELEGRAM_BOT_TOKEN) || empty(TELEGRAM_CHAT_ID)) {
        return false;
    }
    
    $url = "https://api.telegram.org/bot" . TELEGRAM_BOT_TOKEN . "/sendMessage";
    $data = array(
        'chat_id' => TELEGRAM_CHAT_ID,
        'text' => $message,
        'parse_mode' => 'HTML'
    );
    
    $options = array(
        'http' => array(
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        )
    );
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    return $result !== false;
}

/**
 * Generate pagination HTML
 * إنشاء HTML للترقيم
 */
function generate_pagination($current_page, $total_pages, $base_url) {
    if ($total_pages <= 1) return '';
    
    $html = '<nav aria-label="Page navigation"><ul class="pagination justify-content-center">';
    
    // Previous button
    if ($current_page > 1) {
        $html .= '<li class="page-item"><a class="page-link" href="' . $base_url . '?page=' . ($current_page - 1) . '">السابق</a></li>';
    }
    
    // Page numbers
    for ($i = max(1, $current_page - 2); $i <= min($total_pages, $current_page + 2); $i++) {
        $active = ($i == $current_page) ? 'active' : '';
        $html .= '<li class="page-item ' . $active . '"><a class="page-link" href="' . $base_url . '?page=' . $i . '">' . $i . '</a></li>';
    }
    
    // Next button
    if ($current_page < $total_pages) {
        $html .= '<li class="page-item"><a class="page-link" href="' . $base_url . '?page=' . ($current_page + 1) . '">التالي</a></li>';
    }
    
    $html .= '</ul></nav>';
    return $html;
}
?>
