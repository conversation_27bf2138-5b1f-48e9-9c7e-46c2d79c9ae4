<?php
/**
 * Order Class
 * فئة الطلبات
 */

class Order {
    private $conn;
    private $table_name = "orders";

    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create new order
     * إنشاء طلب جديد
     */
    public function createOrder($order_data, $order_items) {
        try {
            $this->conn->beginTransaction();
            
            // Generate order number
            $order_number = generate_order_number();
            
            // Insert order
            $query = "INSERT INTO " . $this->table_name . " 
                      (user_id, order_number, total_amount, shipping_cost, tax_amount, 
                       shipping_address, customer_name, customer_email, customer_phone, notes) 
                      VALUES (:user_id, :order_number, :total_amount, :shipping_cost, :tax_amount,
                              :shipping_address, :customer_name, :customer_email, :customer_phone, :notes)";
            
            $stmt = $this->conn->prepare($query);
            
            $stmt->bindParam(':user_id', $order_data['user_id']);
            $stmt->bindParam(':order_number', $order_number);
            $stmt->bindParam(':total_amount', $order_data['total_amount']);
            $stmt->bindParam(':shipping_cost', $order_data['shipping_cost']);
            $stmt->bindParam(':tax_amount', $order_data['tax_amount']);
            $stmt->bindParam(':shipping_address', $order_data['shipping_address']);
            $stmt->bindParam(':customer_name', $order_data['customer_name']);
            $stmt->bindParam(':customer_email', $order_data['customer_email']);
            $stmt->bindParam(':customer_phone', $order_data['customer_phone']);
            $stmt->bindParam(':notes', $order_data['notes']);
            
            $stmt->execute();
            $order_id = $this->conn->lastInsertId();
            
            // Insert order items
            $item_query = "INSERT INTO order_items 
                           (order_id, product_id, variant_id, product_name, product_sku, 
                            quantity, unit_price, total_price, product_attributes) 
                           VALUES (:order_id, :product_id, :variant_id, :product_name, :product_sku,
                                   :quantity, :unit_price, :total_price, :product_attributes)";
            
            $item_stmt = $this->conn->prepare($item_query);
            
            foreach ($order_items as $item) {
                $item_stmt->bindParam(':order_id', $order_id);
                $item_stmt->bindParam(':product_id', $item['product_id']);
                $item_stmt->bindParam(':variant_id', $item['variant_id']);
                $item_stmt->bindParam(':product_name', $item['product_name']);
                $item_stmt->bindParam(':product_sku', $item['product_sku']);
                $item_stmt->bindParam(':quantity', $item['quantity']);
                $item_stmt->bindParam(':unit_price', $item['unit_price']);
                $item_stmt->bindParam(':total_price', $item['total_price']);
                $item_stmt->bindParam(':product_attributes', $item['product_attributes']);
                
                $item_stmt->execute();
                
                // Update product stock
                $this->updateProductStock($item['product_id'], $item['variant_id'], $item['quantity']);
            }
            
            $this->conn->commit();
            
            // Send Telegram notification
            $this->sendOrderNotification($order_id);
            
            return $order_id;
            
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }

    /**
     * Get order by ID
     * الحصول على طلب بالمعرف
     */
    public function getOrderById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get order items
     * الحصول على عناصر الطلب
     */
    public function getOrderItems($order_id) {
        $query = "SELECT oi.*, p.name as current_product_name 
                  FROM order_items oi 
                  LEFT JOIN products p ON oi.product_id = p.id 
                  WHERE oi.order_id = :order_id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':order_id', $order_id);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get user orders
     * الحصول على طلبات المستخدم
     */
    public function getUserOrders($user_id, $page = 1, $limit = ORDERS_PER_PAGE) {
        $offset = ($page - 1) * $limit;
        
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE user_id = :user_id 
                  ORDER BY created_at DESC 
                  LIMIT :limit OFFSET :offset";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get all orders (admin)
     * الحصول على جميع الطلبات للإدارة
     */
    public function getAllOrders($page = 1, $limit = ORDERS_PER_PAGE, $status = null) {
        $offset = ($page - 1) * $limit;
        
        $query = "SELECT * FROM " . $this->table_name;
        
        if ($status) {
            $query .= " WHERE status = :status";
        }
        
        $query .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
        
        $stmt = $this->conn->prepare($query);
        
        if ($status) {
            $stmt->bindParam(':status', $status);
        }
        
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Update order status
     * تحديث حالة الطلب
     */
    public function updateOrderStatus($id, $status) {
        $query = "UPDATE " . $this->table_name . " 
                  SET status = :status, updated_at = CURRENT_TIMESTAMP 
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->bindParam(':status', $status);
        
        return $stmt->execute();
    }

    /**
     * Update product stock after order
     * تحديث مخزون المنتج بعد الطلب
     */
    private function updateProductStock($product_id, $variant_id, $quantity) {
        if ($variant_id) {
            // Update variant stock
            $query = "UPDATE product_variants 
                      SET stock_quantity = stock_quantity - :quantity 
                      WHERE id = :variant_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':variant_id', $variant_id);
        } else {
            // Update product stock
            $query = "UPDATE products 
                      SET stock_quantity = stock_quantity - :quantity 
                      WHERE id = :product_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':product_id', $product_id);
        }
        
        $stmt->bindParam(':quantity', $quantity);
        return $stmt->execute();
    }

    /**
     * Send order notification to Telegram
     * إرسال إشعار الطلب لتليجرام
     */
    private function sendOrderNotification($order_id) {
        $order = $this->getOrderById($order_id);
        $items = $this->getOrderItems($order_id);
        
        if (!$order) return false;
        
        $message = "🛒 <b>طلب جديد - رقم: {$order['order_number']}</b>\n\n";
        $message .= "👤 <b>العميل:</b> {$order['customer_name']}\n";
        $message .= "📧 <b>البريد:</b> {$order['customer_email']}\n";
        $message .= "📱 <b>الهاتف:</b> {$order['customer_phone']}\n";
        $message .= "💰 <b>المبلغ الإجمالي:</b> " . format_price($order['total_amount']) . "\n\n";
        
        $message .= "📦 <b>المنتجات:</b>\n";
        foreach ($items as $item) {
            $message .= "• {$item['product_name']} × {$item['quantity']} = " . format_price($item['total_price']) . "\n";
        }
        
        $message .= "\n📍 <b>عنوان الشحن:</b>\n{$order['shipping_address']}";
        
        if ($order['notes']) {
            $message .= "\n\n📝 <b>ملاحظات:</b>\n{$order['notes']}";
        }
        
        return send_telegram_notification($message);
    }

    /**
     * Get order statistics
     * الحصول على إحصائيات الطلبات
     */
    public function getOrderStats() {
        $query = "SELECT 
                    COUNT(*) as total_orders,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
                    COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_orders,
                    COUNT(CASE WHEN status = 'shipped' THEN 1 END) as shipped_orders,
                    COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_orders,
                    SUM(total_amount) as total_revenue,
                    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN total_amount ELSE 0 END) as today_revenue
                  FROM " . $this->table_name;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get total orders count
     * الحصول على العدد الإجمالي للطلبات
     */
    public function getTotalOrdersCount($user_id = null, $status = null) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE 1=1";
        
        if ($user_id) {
            $query .= " AND user_id = :user_id";
        }
        
        if ($status) {
            $query .= " AND status = :status";
        }
        
        $stmt = $this->conn->prepare($query);
        
        if ($user_id) {
            $stmt->bindParam(':user_id', $user_id);
        }
        
        if ($status) {
            $stmt->bindParam(':status', $status);
        }
        
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total'];
    }
}
?>
