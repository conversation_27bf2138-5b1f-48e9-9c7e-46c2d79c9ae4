<?php
/**
 * Admin Dashboard - لوحة تحكم الإدارة
 */

require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../classes/User.php';
require_once '../classes/Product.php';
require_once '../classes/Category.php';
require_once '../classes/Order.php';

// Require admin privileges
require_admin();

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Initialize classes
$user_class = new User($db);
$product_class = new Product($db);
$category_class = new Category($db);
$order_class = new Order($db);

// Get statistics
$user_stats = $user_class->getUserStats();
$order_stats = $order_class->getOrderStats();

// Get recent orders
$recent_orders = $order_class->getAllOrders(1, 5);

// Get product count
$total_products = $product_class->getTotalCount();

// Get category count
$categories = $category_class->getAllCategories(false);
$total_categories = count($categories);

$page_title = "لوحة تحكم الإدارة - " . SITE_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/admin-rtl.css" rel="stylesheet">
    
    <style>
        .admin-sidebar {
            background: #2c3e50;
            min-height: 100vh;
            padding: 0;
            position: fixed;
            top: 0;
            right: 0;
            width: 250px;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .admin-sidebar.show {
            transform: translateX(0);
        }
        
        .admin-sidebar .nav-link {
            color: #bdc3c7;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #34495e;
            transition: all 0.3s;
        }
        
        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            background: #3498db;
            color: white;
        }
        
        .admin-sidebar .nav-link i {
            width: 20px;
            margin-left: 10px;
        }
        
        .main-content {
            margin-right: 0;
            transition: margin-right 0.3s ease;
        }
        
        .main-content.sidebar-open {
            margin-right: 250px;
        }
        
        .stat-card {
            border: none;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            color: white;
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card.primary { background: linear-gradient(135deg, #3498db, #2980b9); }
        .stat-card.success { background: linear-gradient(135deg, #2ecc71, #27ae60); }
        .stat-card.warning { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .stat-card.danger { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .dashboard-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .admin-header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 999;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #495057;
        }
        
        .order-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-processing { background: #cce5ff; color: #004085; }
        .status-shipped { background: #d4edda; color: #155724; }
        .status-delivered { background: #d1ecf1; color: #0c5460; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <button class="sidebar-toggle ms-3" id="sidebarToggle">
                        <i class="fas fa-bars ms-1"></i> </button>
                    <h4 class="mb-0">لوحة تحكم الإدارة</h4>
                </div>
                
                <div class="d-flex align-items-center">
                    <span class="ms-3">مرحباً، <?php echo $_SESSION['user_name']; ?></span>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user ms-1"></i> </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>">عرض الموقع</a></li>
                            <li><a class="dropdown-item" href="../user/profile.php">الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php">تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="admin-sidebar" id="adminSidebar">
        <div class="p-3 text-center border-bottom border-secondary">
            <h5 class="text-white mb-0">
                <i class="fas fa-cog ms-2 ms-1"></i>الإدارة
            </h5>
        </div>
        
        <nav class="nav flex-column">
            <a class="nav-link active" href="index.php">
                <i class="fas fa-tachometer-alt ms-1"></i>الرئيسية
            </a>
            <a class="nav-link" href="products/">
                <i class="fas fa-box ms-1"></i>المنتجات
            </a>
            <a class="nav-link" href="categories/">
                <i class="fas fa-tags ms-1"></i>التصنيفات
            </a>
            <a class="nav-link" href="orders/">
                <i class="fas fa-shopping-bag ms-1"></i>الطلبات
            </a>
            <a class="nav-link" href="users/">
                <i class="fas fa-users ms-1"></i>المستخدمين
            </a>
            <a class="nav-link" href="homepage/">
                <i class="fas fa-home ms-1"></i>الصفحة الرئيسية
            </a>
            <a class="nav-link" href="settings/">
                <i class="fas fa-cogs ms-1"></i>الإعدادات
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <div class="container-fluid py-4">
            <!-- Statistics Cards -->
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-users ms-1"></i> </div>
                        <h3><?php echo $user_stats['total_users']; ?></h3>
                        <p class="mb-0">إجمالي المستخدمين</p>
                        <small>جديد اليوم: <?php echo $user_stats['today_registrations']; ?></small>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-box ms-1"></i> </div>
                        <h3><?php echo $total_products; ?></h3>
                        <p class="mb-0">إجمالي المنتجات</p>
                        <small><?php echo $total_categories; ?> تصنيف</small>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-bag ms-1"></i> </div>
                        <h3><?php echo $order_stats['total_orders']; ?></h3>
                        <p class="mb-0">إجمالي الطلبات</p>
                        <small>قيد الانتظار: <?php echo $order_stats['pending_orders']; ?></small>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card danger">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave ms-1"></i> </div>
                        <h3><?php echo format_price($order_stats['total_revenue'] ?: 0); ?></h3>
                        <p class="mb-0">إجمالي المبيعات</p>
                        <small>اليوم: <?php echo format_price($order_stats['today_revenue'] ?: 0); ?></small>
                    </div>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="dashboard-card card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-shopping-bag ms-2 ms-1"></i>الطلبات الأخيرة
                            </h5>
                            <a href="orders/" class="btn btn-outline-primary btn-sm">
                                عرض جميع الطلبات
                            </a>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recent_orders)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>رقم الطلب</th>
                                                <th>العميل</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                                <th>التاريخ</th>
                                                <th>العمليات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_orders as $order): ?>
                                                <tr>
                                                    <td><strong><?php echo $order['order_number']; ?></strong></td>
                                                    <td><?php echo $order['customer_name']; ?></td>
                                                    <td><?php echo format_price($order['total_amount']); ?></td>
                                                    <td>
                                                        <?php
                                                        $status_class = 'status-' . $order['status'];
                                                        $status_text = '';
                                                        switch ($order['status']) {
                                                            case 'pending': $status_text = 'قيد الانتظار'; break;
                                                            case 'processing': $status_text = 'قيد المعالجة'; break;
                                                            case 'shipped': $status_text = 'تم الشحن'; break;
                                                            case 'delivered': $status_text = 'تم التسليم'; break;
                                                            case 'cancelled': $status_text = 'ملغي'; break;
                                                        }
                                                        ?>
                                                        <span class="order-status <?php echo $status_class; ?>">
                                                            <?php echo $status_text; ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo date('Y/m/d', strtotime($order['created_at'])); ?></td>
                                                    <td>
                                                        <a href="orders/view.php?id=<?php echo $order['id']; ?>" 
                                                           class="btn btn-outline-primary btn-sm">
                                                            <i class="fas fa-eye ms-1"></i> </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-shopping-bag fa-3x text-muted mb-3 ms-1"></i> <h5>لا توجد طلبات بعد</h5>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="col-lg-4">
                    <div class="dashboard-card card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt ms-2 ms-1"></i>إجراءات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="products/add.php" class="btn btn-primary">
                                    <i class="fas fa-plus ms-2 ms-1"></i>إضافة منتج جديد
                                </a>
                                
                                <a href="categories/add.php" class="btn btn-success">
                                    <i class="fas fa-plus ms-2 ms-1"></i>إضافة تصنيف جديد
                                </a>
                                
                                <a href="orders/" class="btn btn-warning">
                                    <i class="fas fa-shopping-bag ms-2 ms-1"></i>إدارة الطلبات
                                </a>
                                
                                <a href="homepage/" class="btn btn-info">
                                    <i class="fas fa-home ms-2 ms-1"></i>تحرير الصفحة الرئيسية
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- System Status -->
                    <div class="dashboard-card card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-server ms-2 ms-1"></i>حالة النظام
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>قاعدة البيانات:</span>
                                <span class="badge bg-success">متصلة</span>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>مساحة التخزين:</span>
                                <span class="badge bg-info">متاحة</span>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>إشعارات تليجرام:</span>
                                <span class="badge bg-<?php echo (TELEGRAM_BOT_TOKEN && TELEGRAM_CHAT_ID) ? 'success' : 'warning'; ?>">
                                    <?php echo (TELEGRAM_BOT_TOKEN && TELEGRAM_CHAT_ID) ? 'مفعلة' : 'غير مفعلة'; ?>
                                </span>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <span>آخر نسخة احتياطية:</span>
                                <span class="badge bg-secondary">لم يتم</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <div class="col-12">
                    <div class="dashboard-card card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line ms-2 ms-1"></i>نظرة عامة على الأداء
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3 mb-3">
                                    <h6>الطلبات المعلقة</h6>
                                    <h3 class="text-warning"><?php echo $order_stats['pending_orders']; ?></h3>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <h6>قيد المعالجة</h6>
                                    <h3 class="text-info"><?php echo $order_stats['processing_orders']; ?></h3>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <h6>تم الشحن</h6>
                                    <h3 class="text-primary"><?php echo $order_stats['shipped_orders']; ?></h3>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <h6>تم التسليم</h6>
                                    <h3 class="text-success"><?php echo $order_stats['delivered_orders']; ?></h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Sidebar toggle
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('adminSidebar');
            const mainContent = document.getElementById('mainContent');
            
            sidebar.classList.toggle('show');
            mainContent.classList.toggle('sidebar-open');
        });
        
        // Close sidebar when clicking outside
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('adminSidebar');
            const toggle = document.getElementById('sidebarToggle');
            
            if (!sidebar.contains(e.target) && !toggle.contains(e.target)) {
                sidebar.classList.remove('show');
                document.getElementById('mainContent').classList.remove('sidebar-open');
            }
        });
        
        // Auto-refresh statistics every 30 seconds
        setInterval(function() {
            // You can implement AJAX refresh here
            console.log('Refreshing statistics...');
        }, 30000);
    </script>
</body>
</html>
