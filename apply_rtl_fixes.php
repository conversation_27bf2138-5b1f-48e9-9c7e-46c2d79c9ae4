<?php
/**
 * Apply RTL Fixes to All Admin Pages
 * تطبيق إصلاحات RTL على جميع صفحات الأدمن
 */

echo "<h1>تطبيق إصلاحات RTL</h1>";

// List of admin files to update
$admin_files = [
    'admin/index.php',
    'admin/products/add.php',
    'admin/categories/index.php',
    'admin/categories/add.php',
    'admin/categories/edit.php',
    'admin/orders/index.php',
    'admin/users/index.php',
    'admin/settings.php'
];

$updated_count = 0;
$error_count = 0;

foreach ($admin_files as $file) {
    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
    echo "<strong>معالجة: $file</strong><br>";
    
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $original_content = $content;
        
        // Add admin RTL CSS if not present
        if (strpos($content, 'admin-rtl.css') === false && strpos($content, 'bootstrap') !== false) {
            $css_link = '<link href="../../assets/css/admin-rtl.css" rel="stylesheet">';
            if (strpos($file, 'admin/index.php') !== false) {
                $css_link = '<link href="assets/css/admin-rtl.css" rel="stylesheet">';
            }
            
            $content = str_replace(
                '<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">',
                '<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">' . "\n    " . $css_link,
                $content
            );
        }
        
        // Fix icon margins (me-2 to ms-2 for RTL)
        $content = preg_replace('/me-(\d+)/', 'ms-$1', $content);
        
        // Fix dropdown menus to align right
        $content = str_replace('dropdown-menu"', 'dropdown-menu dropdown-menu-end"', $content);
        
        // Fix button icons
        $content = preg_replace('/<i class="fas fa-([^"]+)"><\/i>\s*([^<]+)/', '<i class="fas fa-$1 ms-1"></i>$2', $content);
        
        if ($content !== $original_content) {
            if (file_put_contents($file, $content)) {
                echo "✅ تم تحديث الملف<br>";
                $updated_count++;
            } else {
                echo "❌ فشل في تحديث الملف<br>";
                $error_count++;
            }
        } else {
            echo "⚠️ لا يحتاج تحديث<br>";
        }
    } else {
        echo "❌ الملف غير موجود<br>";
        $error_count++;
    }
    
    echo "</div>";
}

echo "<h2>ملخص التحديث</h2>";
echo "<div style='background: " . ($error_count > 0 ? '#f8d7da' : '#d4edda') . "; padding: 15px; border-radius: 5px;'>";
echo "عدد الملفات المُحدثة: $updated_count<br>";
echo "عدد الأخطاء: $error_count<br>";

if ($updated_count > 0) {
    echo "<strong>✅ تم تحديث $updated_count ملف بنجاح!</strong><br>";
    echo "الآن جميع صفحات الأدمن تدعم RTL بشكل أفضل.";
}
echo "</div>";

echo "<br><a href='admin/' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب للأدمن بانل</a>";
?>
