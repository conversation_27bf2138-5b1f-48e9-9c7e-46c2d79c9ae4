<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إضافة للسلة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>اختبار إضافة للسلة</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>اختبار مباشر</h3>
                <button class="btn btn-primary" onclick="testAddToCart(1)">
                    اختبار إضافة المنتج رقم 1
                </button>
                
                <div id="result" class="mt-3"></div>
            </div>
            
            <div class="col-md-6">
                <h3>اختبار بالنموذج</h3>
                <form method="POST" action="api/cart.php">
                    <input type="hidden" name="action" value="add">
                    <input type="hidden" name="product_id" value="1">
                    <input type="hidden" name="quantity" value="1">
                    <button type="submit" class="btn btn-success">إضافة عبر النموذج</button>
                </form>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="index.php" class="btn btn-secondary">العودة للصفحة الرئيسية</a>
        </div>
    </div>

    <script>
        function testAddToCart(productId) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="alert alert-info">جاري الاختبار...</div>';
            
            console.log('Testing add to cart for product:', productId);
            
            fetch('api/cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=add&product_id=${productId}&quantity=1`
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Raw response:', text);
                try {
                    const data = JSON.parse(text);
                    console.log('Parsed response:', data);
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="alert alert-success">
                                <strong>نجح!</strong> ${data.message}
                                <br>عدد عناصر السلة: ${data.cart_count || 'غير محدد'}
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="alert alert-danger">
                                <strong>فشل!</strong> ${data.message}
                            </div>
                        `;
                    }
                } catch (e) {
                    console.error('JSON parse error:', e);
                    resultDiv.innerHTML = `
                        <div class="alert alert-warning">
                            <strong>خطأ في التحليل!</strong>
                            <br>الاستجابة الخام: <pre>${text}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>خطأ في الشبكة!</strong> ${error.message}
                    </div>
                `;
            });
        }
    </script>
</body>
</html>
