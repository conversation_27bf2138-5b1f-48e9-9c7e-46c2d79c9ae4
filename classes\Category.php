<?php
/**
 * Category Class
 * فئة التصنيفات
 */

class Category {
    private $conn;
    private $table_name = "categories";

    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all categories
     * الحصول على جميع التصنيفات
     */
    public function getAllCategories($active_only = true) {
        $query = "SELECT * FROM " . $this->table_name;
        
        if ($active_only) {
            $query .= " WHERE is_active = 1";
        }
        
        $query .= " ORDER BY sort_order, name";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get category by ID
     * الحصول على تصنيف بالمعرف
     */
    public function getCategoryById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get main categories (parent categories)
     * الحصول على التصنيفات الرئيسية
     */
    public function getMainCategories() {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE parent_id IS NULL AND is_active = 1 
                  ORDER BY sort_order, name";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get subcategories
     * الحصول على التصنيفات الفرعية
     */
    public function getSubcategories($parent_id) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE parent_id = :parent_id AND is_active = 1 
                  ORDER BY sort_order, name";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':parent_id', $parent_id);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Create new category
     * إنشاء تصنيف جديد
     */
    public function createCategory($data) {
        $query = "INSERT INTO " . $this->table_name . " 
                  (name, description, image, parent_id, sort_order) 
                  VALUES (:name, :description, :image, :parent_id, :sort_order)";
        
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':image', $data['image']);
        $stmt->bindParam(':parent_id', $data['parent_id']);
        $stmt->bindParam(':sort_order', $data['sort_order']);
        
        return $stmt->execute();
    }

    /**
     * Update category
     * تحديث تصنيف
     */
    public function updateCategory($id, $data) {
        $query = "UPDATE " . $this->table_name . " 
                  SET name = :name, description = :description, image = :image, 
                      parent_id = :parent_id, sort_order = :sort_order,
                      updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(':id', $id);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':image', $data['image']);
        $stmt->bindParam(':parent_id', $data['parent_id']);
        $stmt->bindParam(':sort_order', $data['sort_order']);
        
        return $stmt->execute();
    }

    /**
     * Delete category
     * حذف تصنيف
     */
    public function deleteCategory($id) {
        // Check if category has products
        $check_query = "SELECT COUNT(*) as count FROM products WHERE category_id = :id";
        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->bindParam(':id', $id);
        $check_stmt->execute();
        $result = $check_stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            return false; // Cannot delete category with products
        }
        
        // Soft delete
        $query = "UPDATE " . $this->table_name . " SET is_active = 0 WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        
        return $stmt->execute();
    }

    /**
     * Get categories with product count
     * الحصول على التصنيفات مع عدد المنتجات
     */
    public function getCategoriesWithProductCount() {
        $query = "SELECT c.*, COUNT(p.id) as product_count 
                  FROM " . $this->table_name . " c 
                  LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
                  WHERE c.is_active = 1
                  GROUP BY c.id 
                  ORDER BY c.sort_order, c.name";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
