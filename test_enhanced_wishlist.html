<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة المفضلة المحسنة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Swiper CSS -->
    <link href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- <PERSON> Header -->
    <section class="bg-gradient-primary text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-heart me-2"></i>
                        المفضلة
                    </h1>
                    <p class="mb-0 opacity-75">منتجاتك المفضلة</p>
                </div>
                <div class="col-auto">
                    <span class="badge bg-white text-primary fs-6">3 منتجات</span>
                </div>
            </div>
        </div>
    </section>

    <div class="container py-5">
        <div class="row">
            <!-- Wishlist Item 1 -->
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card product-card wishlist-item h-100 animate-fade-in">
                    <!-- Enhanced Image Container -->
                    <div class="product-image-container">
                        <img src="assets/images/products/68b323f5f157c.jpg" class="card-img-top product-image" alt="منتج 1">
                        
                        <!-- Sale Badge -->
                        <div class="sale-badge">
                            <i class="fas fa-tag me-1"></i>
                            خصم 25%
                        </div>

                        <!-- Enhanced Remove Button -->
                        <button class="wishlist-remove-btn" onclick="removeFromWishlistDemo(this)" title="إزالة من المفضلة">
                            <i class="fas fa-heart-broken"></i>
                        </button>


                    </div>
                    
                    <div class="card-body d-flex flex-column product-card-body">
                        <h6 class="card-title product-title">
                            <a href="#" class="text-decoration-none text-dark">هاتف ذكي متطور</a>
                        </h6>
                        
                        <p class="card-text text-muted small flex-grow-1">
                            هاتف ذكي بمواصفات عالية وتقنيات متقدمة...
                        </p>

                        <!-- Rating -->
                        <div class="rating mb-2">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-muted"></i>
                            <small class="text-muted ms-2">(4.0)</small>
                        </div>

                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="price product-price">
                                    <span class="text-danger fw-bold">750 ر.س</span>
                                    <small class="text-muted text-decoration-line-through ms-2">1000 ر.س</small>
                                </div>

                                <!-- Stock Status -->
                                <div class="stock-status">
                                    <small class="text-success">
                                        <i class="fas fa-check-circle ms-1"></i>
                                        متوفر
                                    </small>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="button" class="btn add-to-cart-btn w-100" onclick="addToCartDemo()">
                                    <i class="fas fa-shopping-cart ms-2"></i>
                                    أضف للسلة
                                </button>

                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye ms-2"></i>
                                    عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Wishlist Item 2 -->
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card product-card wishlist-item h-100 animate-fade-in">
                    <div class="product-image-container">
                        <img src="assets/images/products/68b48700dc365.jpeg" class="card-img-top product-image" alt="منتج 2">

                        <button class="wishlist-remove-btn" onclick="removeFromWishlistDemo(this)" title="إزالة من المفضلة">
                            <i class="fas fa-heart-broken"></i>
                        </button>


                    </div>
                    
                    <div class="card-body d-flex flex-column product-card-body">
                        <h6 class="card-title product-title">
                            <a href="#" class="text-decoration-none text-dark">ساعة ذكية أنيقة</a>
                        </h6>
                        
                        <p class="card-text text-muted small flex-grow-1">
                            ساعة ذكية بتصميم عصري ومميزات متقدمة...
                        </p>

                        <div class="rating mb-2">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <small class="text-muted ms-2">(5.0)</small>
                        </div>

                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="price product-price">
                                    <span class="text-primary fw-bold">500 ر.س</span>
                                </div>
                                <div class="stock-status">
                                    <small class="text-danger">
                                        <i class="fas fa-times-circle ms-1"></i>
                                        غير متوفر
                                    </small>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-secondary w-100" disabled>
                                    <i class="fas fa-ban ms-2"></i>
                                    غير متوفر
                                </button>
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye ms-2"></i>
                                    عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Wishlist Item 3 -->
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card product-card wishlist-item h-100 animate-fade-in">
                    <div class="product-image-container">
                        <img src="assets/images/products/68b48700dfcfb.jpeg" class="card-img-top product-image" alt="منتج 3">

                        <button class="wishlist-remove-btn" onclick="removeFromWishlistDemo(this)" title="إزالة من المفضلة">
                            <i class="fas fa-heart-broken"></i>
                        </button>


                    </div>
                    
                    <div class="card-body d-flex flex-column product-card-body">
                        <h6 class="card-title product-title">
                            <a href="#" class="text-decoration-none text-dark">سماعات لاسلكية</a>
                        </h6>
                        
                        <p class="card-text text-muted small flex-grow-1">
                            سماعات لاسلكية بجودة صوت عالية...
                        </p>

                        <div class="rating mb-2">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-muted"></i>
                            <i class="fas fa-star text-muted"></i>
                            <small class="text-muted ms-2">(3.5)</small>
                        </div>

                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="price product-price">
                                    <span class="text-primary fw-bold">300 ر.س</span>
                                </div>
                                <div class="stock-status">
                                    <small class="text-success">
                                        <i class="fas fa-check-circle ms-1"></i>
                                        متوفر
                                    </small>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="button" class="btn add-to-cart-btn w-100" onclick="addToCartDemo()">
                                    <i class="fas fa-shopping-cart ms-2"></i>
                                    أضف للسلة
                                </button>
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye ms-2"></i>
                                    عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Continue Shopping -->
        <div class="text-center mt-5">
            <a href="#" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-shopping-bag me-2"></i>
                متابعة التسوق
            </a>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add staggered animation to wishlist items
            document.querySelectorAll('.wishlist-item').forEach((item, index) => {
                item.style.animationDelay = (index * 0.1) + 's';
            });
        });

        // Demo Functions
        function removeFromWishlistDemo(button) {
            const card = button.closest('.wishlist-item');
            
            // Add removing animation
            card.classList.add('removing');
            
            setTimeout(() => {
                if (confirm('هل تريد إزالة هذا المنتج من المفضلة؟')) {
                    // Simulate removal
                    setTimeout(() => {
                        card.style.display = 'none';
                        showToast('تم إزالة المنتج من المفضلة', 'info');
                    }, 300);
                } else {
                    card.classList.remove('removing');
                }
            }, 300);
        }

        function addToCartDemo() {
            showToast('تم إضافة المنتج للسلة بنجاح', 'success');
        }



        // Toast function
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toast-container');
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0`;
            toast.setAttribute('role', 'alert');
            
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    </script>
</body>
</html>
