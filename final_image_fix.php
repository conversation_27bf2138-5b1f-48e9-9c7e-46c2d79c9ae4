<?php
/**
 * Final Image Fix - الإصلاح النهائي للصور
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/image_helper.php';
require_once 'classes/Product.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();
$product_class = new Product($db);

echo "<h1>الإصلاح النهائي للصور</h1>";

$fixed_count = 0;
$migrated_count = 0;

// Step 1: Ensure all directories exist
echo "<h2>1. إنشاء المجلدات المطلوبة</h2>";
$directories = [
    'assets/images/products',
    'assets/images/categories',
    'assets/images'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0777, true)) {
            echo "✅ تم إنشاء: $dir<br>";
        } else {
            echo "❌ فشل في إنشاء: $dir<br>";
        }
    } else {
        echo "✅ موجود: $dir<br>";
    }
}

// Step 2: Migrate all images from uploads to assets
echo "<h2>2. نقل الصور من uploads إلى assets</h2>";
$migration_result = cleanup_old_images();
echo "تم نقل: " . $migration_result['moved'] . " صورة<br>";
echo "أخطاء: " . $migration_result['errors'] . " خطأ<br>";
$migrated_count = $migration_result['moved'];

// Step 3: Update database records
echo "<h2>3. تحديث قاعدة البيانات</h2>";
$query = "SELECT id, name, primary_image FROM products WHERE primary_image IS NOT NULL AND primary_image != ''";
$stmt = $db->prepare($query);
$stmt->execute();
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($products as $product) {
    $image_name = $product['primary_image'];
    $new_path = 'assets/images/products/' . $image_name;
    $old_path = 'uploads/products/' . $image_name;
    
    // Check if image exists in new location
    if (!file_exists($new_path) && file_exists($old_path)) {
        if (copy($old_path, $new_path)) {
            echo "✅ نُقلت صورة المنتج: " . $product['name'] . "<br>";
            $fixed_count++;
        }
    } elseif (file_exists($new_path)) {
        echo "✅ صورة موجودة: " . $product['name'] . "<br>";
    } else {
        echo "⚠️ صورة مفقودة: " . $product['name'] . " - " . $image_name . "<br>";
        
        // Try to find alternative image
        $images = $product_class->getProductImages($product['id']);
        if (!empty($images)) {
            $alt_image = $images[0]['image_path'];
            $alt_path = 'assets/images/products/' . $alt_image;
            
            if (file_exists($alt_path)) {
                // Update primary image
                $update_query = "UPDATE products SET primary_image = :image WHERE id = :id";
                $update_stmt = $db->prepare($update_query);
                $update_stmt->bindParam(':image', $alt_image);
                $update_stmt->bindParam(':id', $product['id']);
                
                if ($update_stmt->execute()) {
                    echo "✅ تم تحديث الصورة الرئيسية للمنتج: " . $product['name'] . "<br>";
                    $fixed_count++;
                }
            }
        }
    }
}

// Step 4: Test image display
echo "<h2>4. اختبار عرض الصور</h2>";
$test_products = array_slice($products, 0, 3);

echo "<div class='row'>";
foreach ($test_products as $product) {
    echo "<div class='col-md-4 mb-3'>";
    echo "<div class='card'>";
    
    if ($product['primary_image']) {
        echo generate_product_image_html(
            $product['primary_image'], 
            $product['name'], 
            'card-img-top',
            'height: 200px; object-fit: cover;'
        );
    } else {
        echo "<img src='" . ASSETS_URL . "images/placeholder.svg' class='card-img-top' style='height: 200px; object-fit: cover;' alt='لا توجد صورة'>";
    }
    
    echo "<div class='card-body'>";
    echo "<h6 class='card-title'>" . $product['name'] . "</h6>";
    echo "<small class='text-muted'>الصورة: " . ($product['primary_image'] ?: 'افتراضية') . "</small>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}
echo "</div>";

// Step 5: Summary
echo "<h2>ملخص الإصلاح النهائي</h2>";
echo "<div class='alert alert-success'>";
echo "<h5><i class='fas fa-check-circle ms-2'></i>تم بنجاح!</h5>";
echo "<ul>";
echo "<li>تم نقل $migrated_count صورة من uploads إلى assets</li>";
echo "<li>تم إصلاح $fixed_count منتج</li>";
echo "<li>تم إنشاء دوال مساعدة للصور</li>";
echo "<li>تم تحسين CSS للـ RTL</li>";
echo "<li>تم إضافة صورة افتراضية للصور المفقودة</li>";
echo "</ul>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<a href='index.php' class='btn btn-primary me-2'>";
echo "<i class='fas fa-home ms-1'></i> الصفحة الرئيسية";
echo "</a>";
echo "<a href='admin/' class='btn btn-warning'>";
echo "<i class='fas fa-cogs ms-1'></i> الأدمن بانل";
echo "</a>";
echo "</div>";
?>
