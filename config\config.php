<?php
/**
 * General Configuration
 * التكوين العام للموقع
 */

// Site Configuration
define('SITE_NAME', 'Shoppy - متجر إلكتروني');
define('SITE_URL', 'http://localhost/shoppy');
define('ADMIN_EMAIL', '<EMAIL>');

// Paths
define('ROOT_PATH', dirname(__DIR__));
define('UPLOAD_PATH', ROOT_PATH . '/uploads/');
define('PRODUCT_IMAGES_PATH', ROOT_PATH . '/assets/images/products/');

// URLs
define('BASE_URL', '/shoppy/');
define('ADMIN_URL', BASE_URL . 'admin/');
define('ASSETS_URL', BASE_URL . 'assets/');

// Pagination
define('PRODUCTS_PER_PAGE', 12);
define('ORDERS_PER_PAGE', 10);

// File Upload Settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// Telegram Bot Configuration
define('TELEGRAM_BOT_TOKEN', ''); // Add your bot token here
define('TELEGRAM_CHAT_ID', ''); // Add your chat ID here

// Session Configuration
ini_set('session.cookie_lifetime', 86400); // 24 hours
ini_set('session.gc_maxlifetime', 86400);
session_start();

// Error Reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Currency Settings
define('DEFAULT_CURRENCY', 'SAR');
define('SUPPORTED_CURRENCIES', [
    'SAR' => ['symbol' => 'ر.س', 'name' => 'ريال سعودي'],
    'EGP' => ['symbol' => 'ج.م', 'name' => 'جنيه مصري'],
    'USD' => ['symbol' => '$', 'name' => 'دولار أمريكي'],
    'EUR' => ['symbol' => '€', 'name' => 'يورو']
]);

// Timezone
date_default_timezone_set('Asia/Riyadh');

// Include database configuration
require_once 'database.php';
?>
