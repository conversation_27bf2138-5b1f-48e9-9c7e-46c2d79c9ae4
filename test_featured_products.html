<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنتجات المتميزة المحسنة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Swiper CSS -->
    <link href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Featured Products Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-6 fw-bold text-primary mb-3">
                    <i class="fas fa-star me-2"></i>
                    المنتجات المميزة
                </h2>
                <p class="text-muted">اكتشف أفضل منتجاتنا المختارة بعناية</p>
            </div>
            
            <div class="row">
                <!-- Featured Product 1 - With Multiple Images -->
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card product-card h-100">
                        <!-- Enhanced Image Container with Slider -->
                        <div class="product-image-container">
                            <div class="product-image-slider">
                                <div class="swiper product-card-swiper" data-product-id="1">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <img src="assets/images/products/68b323f5f157c.jpg" class="card-img-top product-image" alt="منتج 1">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="assets/images/products/68b329117f2e6.png" class="card-img-top product-image" alt="منتج 1">
                                        </div>
                                        <div class="swiper-slide">
                                            <img src="assets/images/products/68b329117fc27.png" class="card-img-top product-image" alt="منتج 1">
                                        </div>
                                    </div>
                                    <!-- Navigation buttons -->
                                    <div class="swiper-button-next product-slider-next"></div>
                                    <div class="swiper-button-prev product-slider-prev"></div>
                                    <!-- Pagination dots -->
                                    <div class="swiper-pagination product-slider-pagination"></div>
                                </div>
                            </div>

                            <!-- Sale Badge -->
                            <div class="sale-badge">
                                <i class="fas fa-tag me-1"></i>
                                خصم 25%
                            </div>

                            <!-- Featured Badge -->
                            <div class="featured-badge">
                                <i class="fas fa-star me-1"></i>
                                مميز
                            </div>

                            <!-- Enhanced Wishlist Button -->
                            <button class="wishlist-btn" onclick="toggleWishlistDemo(this)" title="إضافة للمفضلة">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        
                        <div class="card-body d-flex flex-column product-card-body">
                            <h6 class="card-title product-title">هاتف ذكي متطور</h6>
                            <p class="card-text text-muted small flex-grow-1">
                                هاتف ذكي بمواصفات عالية وتقنيات متقدمة للاستخدام اليومي...
                            </p>

                            <!-- Rating -->
                            <div class="rating mb-2">
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-muted"></i>
                                <small class="text-muted ms-2">(4.0)</small>
                            </div>

                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="price product-price">
                                        <span class="text-danger fw-bold">750 ر.س</span>
                                        <small class="text-muted text-decoration-line-through ms-2">1000 ر.س</small>
                                    </div>
                                    <div class="stock-status">
                                        <small class="text-success">
                                            <i class="fas fa-check-circle ms-1"></i>
                                            متوفر
                                        </small>
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="button" class="btn add-to-cart-btn w-100" onclick="addToCartDemo()">
                                        <i class="fas fa-shopping-cart ms-2"></i>
                                        أضف للسلة
                                    </button>
                                    <a href="#" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye ms-2"></i>
                                        عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Featured Product 2 - Single Image -->
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card product-card h-100">
                        <div class="product-image-container">
                            <img src="assets/images/products/68b48700dc365.jpeg" class="card-img-top product-image" alt="منتج 2">

                            <!-- Featured Badge -->
                            <div class="featured-badge">
                                <i class="fas fa-star me-1"></i>
                                مميز
                            </div>

                            <button class="wishlist-btn active" onclick="toggleWishlistDemo(this)" title="إضافة للمفضلة">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                        
                        <div class="card-body d-flex flex-column product-card-body">
                            <h6 class="card-title product-title">ساعة ذكية أنيقة</h6>
                            <p class="card-text text-muted small flex-grow-1">
                                ساعة ذكية بتصميم عصري ومميزات متقدمة...
                            </p>

                            <div class="rating mb-2">
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <small class="text-muted ms-2">(5.0)</small>
                            </div>

                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="price product-price">
                                        <span class="text-primary fw-bold">500 ر.س</span>
                                    </div>
                                    <div class="stock-status">
                                        <small class="text-warning">
                                            <i class="fas fa-exclamation-triangle ms-1"></i>
                                            كمية محدودة
                                        </small>
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="button" class="btn add-to-cart-btn w-100" onclick="addToCartDemo()">
                                        <i class="fas fa-shopping-cart ms-2"></i>
                                        أضف للسلة
                                    </button>
                                    <a href="#" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye ms-2"></i>
                                        عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Featured Product 3 -->
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card product-card h-100">
                        <div class="product-image-container">
                            <img src="assets/images/products/68b48700dfcfb.jpeg" class="card-img-top product-image" alt="منتج 3">

                            <!-- Featured Badge -->
                            <div class="featured-badge">
                                <i class="fas fa-star me-1"></i>
                                مميز
                            </div>

                            <button class="wishlist-btn" onclick="toggleWishlistDemo(this)" title="إضافة للمفضلة">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        
                        <div class="card-body d-flex flex-column product-card-body">
                            <h6 class="card-title product-title">سماعات لاسلكية</h6>
                            <p class="card-text text-muted small flex-grow-1">
                                سماعات لاسلكية بجودة صوت عالية...
                            </p>

                            <div class="rating mb-2">
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-muted"></i>
                                <i class="fas fa-star text-muted"></i>
                                <small class="text-muted ms-2">(3.5)</small>
                            </div>

                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="price product-price">
                                        <span class="text-primary fw-bold">300 ر.س</span>
                                    </div>
                                    <div class="stock-status">
                                        <small class="text-success">
                                            <i class="fas fa-check-circle ms-1"></i>
                                            متوفر
                                        </small>
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="button" class="btn add-to-cart-btn w-100" onclick="addToCartDemo()">
                                        <i class="fas fa-shopping-cart ms-2"></i>
                                        أضف للسلة
                                    </button>
                                    <a href="#" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye ms-2"></i>
                                        عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Featured Product 4 -->
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card product-card h-100">
                        <div class="product-image-container">
                            <img src="assets/images/placeholder.svg" class="card-img-top product-image" alt="منتج 4">

                            <!-- Featured Badge -->
                            <div class="featured-badge">
                                <i class="fas fa-star me-1"></i>
                                مميز
                            </div>

                            <button class="wishlist-btn" onclick="toggleWishlistDemo(this)" title="إضافة للمفضلة">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        
                        <div class="card-body d-flex flex-column product-card-body">
                            <h6 class="card-title product-title">منتج مميز جديد</h6>
                            <p class="card-text text-muted small flex-grow-1">
                                منتج جديد ومميز بمواصفات رائعة...
                            </p>

                            <div class="rating mb-2">
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <i class="fas fa-star text-warning"></i>
                                <small class="text-muted ms-2">(4.8)</small>
                            </div>

                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="price product-price">
                                        <span class="text-primary fw-bold">200 ر.س</span>
                                    </div>
                                    <div class="stock-status">
                                        <small class="text-success">
                                            <i class="fas fa-check-circle ms-1"></i>
                                            متوفر
                                        </small>
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="button" class="btn add-to-cart-btn w-100" onclick="addToCartDemo()">
                                        <i class="fas fa-shopping-cart ms-2"></i>
                                        أضف للسلة
                                    </button>
                                    <a href="#" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye ms-2"></i>
                                        عرض التفاصيل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="text-center mt-5">
                <a href="#" class="btn btn-primary btn-lg">
                    <i class="fas fa-shopping-bag me-2"></i>
                    عرض جميع المنتجات
                </a>
            </div>
        </div>
    </section>

    <!-- Toast Container -->
    <div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    
    <script>
        // Initialize everything
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize product card sliders
            document.querySelectorAll('.product-card-swiper').forEach((swiperEl, index) => {
                new Swiper(swiperEl, {
                    slidesPerView: 1,
                    spaceBetween: 0,
                    loop: true,
                    autoplay: {
                        delay: 4000,
                        disableOnInteraction: false,
                        pauseOnMouseEnter: true,
                    },
                    navigation: {
                        nextEl: swiperEl.querySelector('.product-slider-next'),
                        prevEl: swiperEl.querySelector('.product-slider-prev'),
                    },
                    pagination: {
                        el: swiperEl.querySelector('.product-slider-pagination'),
                        clickable: true,
                        dynamicBullets: true,
                    },
                    effect: 'fade',
                    fadeEffect: {
                        crossFade: true
                    },
                    speed: 600,
                });
            });

            console.log('Featured products initialized successfully');
        });

        // Demo Functions
        function toggleWishlistDemo(button) {
            const icon = button.querySelector('i');
            const isActive = button.classList.contains('active');

            // Add loading state
            button.style.pointerEvents = 'none';
            icon.className = 'fas fa-spinner fa-spin';

            setTimeout(() => {
                if (isActive) {
                    button.classList.remove('active');
                    icon.classList.remove('fas', 'fa-spinner', 'fa-spin');
                    icon.classList.add('far', 'fa-heart');
                    showToast('تم إزالة المنتج من المفضلة', 'info');
                } else {
                    button.classList.add('active');
                    icon.classList.remove('far', 'fa-spinner', 'fa-spin');
                    icon.classList.add('fas', 'fa-heart');
                    showToast('تم إضافة المنتج للمفضلة', 'success');

                    // Add heart animation
                    button.style.animation = 'heartPulse 0.8s ease';
                    setTimeout(() => {
                        button.style.animation = '';
                    }, 800);
                }
                button.style.pointerEvents = 'auto';
            }, 500);
        }

        function addToCartDemo() {
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';

            setTimeout(() => {
                showToast('تم إضافة المنتج للسلة بنجاح', 'success');
                button.disabled = false;
                button.innerHTML = originalText;
                
                button.style.animation = 'bounceIn 0.6s ease';
                setTimeout(() => {
                    button.style.animation = '';
                }, 600);
            }, 800);
        }

        // Toast function
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toast-container');
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0`;
            toast.setAttribute('role', 'alert');
            
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    </script>
</body>
</html>
