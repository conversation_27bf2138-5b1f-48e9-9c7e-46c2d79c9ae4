<?php
/**
 * Enhanced Navigation Bar - شريط التنقل المحسن
 */

// Get current page for active states
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$current_category = $_GET['category'] ?? '';
?>

<!-- Enhanced Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-gradient-primary sticky-top shadow-lg">
    <div class="container">
        <!-- Brand -->
        <a class="navbar-brand fw-bold" href="<?php echo BASE_URL; ?>">
            <i class="fas fa-shopping-bag ms-2 text-warning"></i>
            <span class="text-gradient-light"><?php echo SITE_NAME; ?></span>
        </a>
        
        <!-- Mobile Toggle -->
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- Main Navigation -->
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_page == 'index' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>">
                        <i class="fas fa-home ms-1"></i>
                        الرئيسية
                    </a>
                </li>

                <!-- Categories Dropdown -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo $current_category ? 'active' : ''; ?>"
                       href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-tags ms-1"></i>
                        التصنيفات
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end shadow-lg border-0">
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>">
                                <i class="fas fa-th-large ms-2 text-primary"></i>
                                جميع المنتجات
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <?php
                        // Get categories for dropdown
                        if (isset($category) && $category instanceof Category) {
                            $nav_categories = $category->getAllCategories();
                        } else {
                            require_once 'classes/Category.php';
                            $database = new Database();
                            $db = $database->getConnection();
                            $nav_category = new Category($db);
                            $nav_categories = $nav_category->getAllCategories();
                        }

                        foreach ($nav_categories as $cat):
                        ?>
                            <li>
                                <a class="dropdown-item <?php echo $current_category == $cat['id'] ? 'active' : ''; ?>"
                                   href="<?php echo BASE_URL; ?>?category=<?php echo $cat['id']; ?>">
                                    <i class="fas fa-folder ms-2 text-info"></i>
                                    <?php echo $cat['name']; ?>
                                    <?php if ($cat['product_count'] > 0): ?>
                                        <span class="badge bg-secondary me-1"><?php echo $cat['product_count']; ?></span>
                                    <?php endif; ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </li>

                <!-- Offers Link -->
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo BASE_URL; ?>?offers=1">
                        <i class="fas fa-fire ms-1 text-warning"></i>
                        العروض
                    </a>
                </li>

                <!-- About Dropdown -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-info-circle ms-1"></i>
                        حول الموقع
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end shadow-lg border-0">
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>about.php">
                                <i class="fas fa-users ms-2 text-success"></i>
                                من نحن
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>contact.php">
                                <i class="fas fa-envelope ms-2 text-warning"></i>
                                اتصل بنا
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>privacy.php">
                                <i class="fas fa-shield-alt ms-2 text-info"></i>
                                سياسة الخصوصية
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>terms.php">
                                <i class="fas fa-file-contract ms-2 text-secondary"></i>
                                الشروط والأحكام
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>

            <!-- Search Form -->
            <form class="d-flex me-3 search-form" method="GET" action="<?php echo BASE_URL; ?>">
                <div class="input-group">
                    <input class="form-control border-0 bg-white bg-opacity-90"
                           type="search"
                           name="search"
                           placeholder="البحث عن المنتجات..."
                           value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                    <button class="btn btn-light border-0" type="submit">
                        <i class="fas fa-search text-primary"></i>
                    </button>
                </div>
            </form>

            <!-- Right Side Navigation -->
            <ul class="navbar-nav">
                <!-- Cart -->
                <li class="nav-item">
                    <a class="nav-link position-relative cart-link" href="<?php echo BASE_URL; ?>cart.php">
                        <i class="fas fa-shopping-cart ms-1"></i>
                        السلة
                        <?php
                        $cart_count = get_cart_count();
                        if ($cart_count > 0):
                        ?>
                            <span class="cart-badge animate__animated animate__pulse animate__infinite">
                                <?php echo $cart_count; ?>
                            </span>
                        <?php endif; ?>
                    </a>
                </li>

                <!-- User Menu -->
                <?php if (is_logged_in()): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user ms-1"></i>
                            <?php echo $_SESSION['user_name']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow-lg border-0">
                            <li>
                                <a class="dropdown-item" href="<?php echo BASE_URL; ?>user/dashboard.php">
                                    <i class="fas fa-tachometer-alt ms-2 text-primary"></i>
                                    لوحة التحكم
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="<?php echo BASE_URL; ?>user/orders.php">
                                    <i class="fas fa-shopping-bag ms-2 text-success"></i>
                                    طلباتي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="<?php echo BASE_URL; ?>user/profile.php">
                                    <i class="fas fa-user-edit ms-2 text-info"></i>
                                    الملف الشخصي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="<?php echo BASE_URL; ?>user/wishlist.php">
                                    <i class="fas fa-heart ms-2 text-danger"></i>
                                    المفضلة
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <?php if (is_admin()): ?>
                                <li>
                                    <a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/">
                                        <i class="fas fa-cogs ms-2 text-warning"></i>
                                        لوحة الإدارة
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                            <?php endif; ?>
                            <li>
                                <a class="dropdown-item text-danger" href="<?php echo BASE_URL; ?>auth/logout.php">
                                    <i class="fas fa-sign-out-alt ms-2"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </li>
                <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>auth/login.php">
                            <i class="fas fa-sign-in-alt ms-1"></i>
                            تسجيل الدخول
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>auth/register.php">
                            <i class="fas fa-user-plus ms-1"></i>
                            إنشاء حساب
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav>

<!-- Enhanced CSS for Navigation -->
<style>
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.text-gradient-light {
    background: linear-gradient(45deg, #fff, #f8f9fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    margin: 0 0.25rem;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

.dropdown-menu {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.dropdown-item {
    border-radius: 0.5rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateX(-5px);
}

.dropdown-item.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.search-form .form-control {
    border-radius: 2rem 0 0 2rem;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-form .btn {
    border-radius: 0 2rem 2rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.cart-link {
    position: relative;
}

.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    font-size: 11px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* Mobile Responsive */
@media (max-width: 991px) {
    .navbar-nav {
        text-align: right;
        margin-top: 1rem;
    }

    .search-form {
        margin: 1rem 0;
        width: 100%;
    }

    .dropdown-menu {
        position: static !important;
        transform: none !important;
        box-shadow: none;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 0.5rem;
        margin-top: 0.5rem;
    }

    .dropdown-item {
        color: rgba(255, 255, 255, 0.9);
    }

    .dropdown-item:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
    }
}

/* Animation for dropdown */
.dropdown-menu {
    animation: fadeInDown 0.3s ease;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<!-- JavaScript for Enhanced Navigation -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Auto-hide navbar on scroll down, show on scroll up
    let lastScrollTop = 0;
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down
            navbar.style.transform = 'translateY(-100%)';
        } else {
            // Scrolling up
            navbar.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
    });
    
    // Add transition to navbar
    navbar.style.transition = 'transform 0.3s ease';
    
    // Update cart count dynamically
    function updateCartCount() {
        fetch('<?php echo BASE_URL; ?>api/cart.php?action=get_count')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const cartBadge = document.querySelector('.cart-badge');
                if (data.count > 0) {
                    if (cartBadge) {
                        cartBadge.textContent = data.count;
                    } else {
                        // Create badge if it doesn't exist
                        const cartLink = document.querySelector('.cart-link');
                        const badge = document.createElement('span');
                        badge.className = 'cart-badge animate__animated animate__pulse animate__infinite';
                        badge.textContent = data.count;
                        cartLink.appendChild(badge);
                    }
                } else if (cartBadge) {
                    cartBadge.remove();
                }
            }
        })
        .catch(error => console.error('Error updating cart count:', error));
    }
    
    // Update cart count every 30 seconds
    setInterval(updateCartCount, 30000);
});
</script>
