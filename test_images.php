<?php
/**
 * Test Images Display - اختبار عرض الصور
 */

require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'classes/Product.php';

// Initialize database connection
$database = new Database();
$db = $database->getConnection();
$product_class = new Product($db);

echo "<h1>اختبار عرض الصور</h1>";

// Test configuration
echo "<h2>1. اختبار التكوين</h2>";
echo "BASE_URL: " . BASE_URL . "<br>";
echo "ASSETS_URL: " . ASSETS_URL . "<br>";
echo "PRODUCT_IMAGES_PATH: " . PRODUCT_IMAGES_PATH . "<br>";

// Test image directory
echo "<h2>2. فحص مجلد الصور</h2>";
$image_dir = 'assets/images/products/';
echo "مجلد الصور موجود: " . (is_dir($image_dir) ? 'نعم' : 'لا') . "<br>";

if (is_dir($image_dir)) {
    $images = scandir($image_dir);
    $image_files = array_filter($images, function($file) {
        return !in_array($file, ['.', '..']) && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $file);
    });
    
    echo "عدد الصور الموجودة: " . count($image_files) . "<br>";
    echo "أسماء الصور: " . implode(', ', array_slice($image_files, 0, 5)) . "<br>";
}

// Test products with images
echo "<h2>3. اختبار المنتجات مع الصور</h2>";
$products = $product_class->getProducts(1, 5);

echo "<table border='1' style='width: 100%; border-collapse: collapse;'>";
echo "<tr><th>ID</th><th>الاسم</th><th>الصورة الرئيسية</th><th>معاينة</th><th>مسار كامل</th></tr>";

foreach ($products as $product) {
    echo "<tr>";
    echo "<td>" . $product['id'] . "</td>";
    echo "<td>" . $product['name'] . "</td>";
    echo "<td>" . ($product['primary_image'] ?: 'لا توجد') . "</td>";
    
    if ($product['primary_image']) {
        $image_url = ASSETS_URL . 'images/products/' . $product['primary_image'];
        $image_path = $image_dir . $product['primary_image'];
        
        echo "<td>";
        if (file_exists($image_path)) {
            echo "<img src='$image_url' style='width: 50px; height: 50px; object-fit: cover;' alt='صورة'>";
        } else {
            echo "❌ الملف غير موجود";
        }
        echo "</td>";
        
        echo "<td>";
        echo "URL: $image_url<br>";
        echo "Path: $image_path<br>";
        echo "موجود: " . (file_exists($image_path) ? 'نعم' : 'لا');
        echo "</td>";
    } else {
        echo "<td>لا توجد صورة</td>";
        echo "<td>-</td>";
    }
    
    echo "</tr>";
}
echo "</table>";

// Test direct image access
echo "<h2>4. اختبار الوصول المباشر للصور</h2>";
if (!empty($image_files)) {
    $test_image = array_values($image_files)[0];
    $test_url = ASSETS_URL . 'images/products/' . $test_image;
    
    echo "اختبار الصورة: $test_image<br>";
    echo "الرابط: $test_url<br>";
    echo "<img src='$test_url' style='max-width: 200px; border: 1px solid #ccc;' alt='اختبار'><br>";
}

echo "<br><a href='index.php'>العودة للصفحة الرئيسية</a>";
?>
